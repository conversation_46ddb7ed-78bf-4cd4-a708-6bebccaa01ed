package com.xt.hsk.module.edu.controller.admin.word.vo;

import com.xt.hsk.framework.common.pojo.ExportPageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WordPageReqVO extends ExportPageParam {
    /**
     * id
     */
    private Long id;

    /**
     * 汉字/词语（如爱）
     */
    private String word;

    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;
    /**
     * Hsk等级
     */
    private List<Integer> hskLevels;
    /**
     * 词性
     */
    private List<String> kinds;
    /**
     * 标签
     */
    private List<Long> tags;
    /**
     * 英文
     */
    private String translationEn;
    /**
     * 越南语
     */
    private String translationOt;
    /**
     * 最近更新时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime[] updateTime;

    /**
     * 是否只搜索一个字 true 精确匹配 false 模糊匹配
     */
    private Boolean singleChar;

}