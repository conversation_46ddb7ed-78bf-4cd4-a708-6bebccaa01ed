package com.xt.hsk.module.edu.controller.admin.word;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitQuoteVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordExportReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordRespVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordSaveReqVO;
import com.xt.hsk.module.edu.service.interactivecourse.InteractiveCourseService;
import com.xt.hsk.module.edu.service.word.WordManager;
import com.xt.hsk.module.game.api.SpecialExerciseApi;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteDTO;
import com.xt.hsk.module.game.api.dto.SpecialExerciseOtQuoteReqDTO;
import com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 汉语词典基础数据")
@RestController
@RequestMapping("/edu/word")
@Validated
@Slf4j
public class WordController {

    @Resource
    private WordManager wordManager;

    @Resource
    private ExportTaskApi exportTaskApi;
    @Resource
    private SpecialExerciseApi specialExerciseApi;
    @Resource
    private InteractiveCourseService interactiveCourseService;

    @PostMapping("/create")
    @Operation(summary = "创建汉语词典基础数据")
    @PreAuthorize("@ss.hasPermission('edu:word:create')")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordType.WORD_TYPE,
        subType = "创建字词",
        bizNo = "{{#wordId}}", success = "创建字词：【{{#word.word}}】，HSK等级：{{#word.hskLevel}}")
    public CommonResult<Long> createWord(@Valid @RequestBody WordSaveReqVO createReqVO) {
        return success(wordManager.createWord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新汉语词典基础数据")
    @PreAuthorize("@ss.hasPermission('edu:word:update')")
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordType.WORD_TYPE,
        subType = "修改字词",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改字词：【{{#word.word}}】，HSK等级：{{#word.hskLevel}}")
    public CommonResult<Boolean> updateWord(@Valid @RequestBody WordSaveReqVO updateReqVO) {
        wordManager.updateWord(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除汉语词典基础数据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:word:delete')")
    @LogRecord(type = LogRecordType.WORD_TYPE,
        subType = "删除字词",
        bizNo = "{{#id}}",
        success = "删除字词：【{{#word.wordCn}}】，HSK等级：{{#word.hskLevel}}")
    public CommonResult<Boolean> deleteWord(@RequestParam("id") Long id) {
        wordManager.deleteWord(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得汉语词典基础数据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:word:query')")
    @TransMethodResult
    public CommonResult<WordRespVO> getWord(@RequestParam("id") Long id) {
        WordRespVO word = wordManager.getWord(id);
        return success(word);
    }

    @PostMapping("/page")
    @Operation(summary = "获得汉语词典基础数据分页")
    @PreAuthorize("@ss.hasPermission('edu:word:query')")
    @TransMethodResult()
    public CommonResult<PageResult<WordRespVO>> getWordPage(@Valid @RequestBody WordPageReqVO pageReqVO) {
        PageResult<WordRespVO> pageResult = wordManager.getWordPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 获取题目引用信息
     * @param wordId 词条id
     */
    @PostMapping("/getQuoteInfo")
    @Operation(summary = "获取题目引用信息")
    public CommonResult<List<Integer>> getQuoteInfo(@RequestParam("wordId") Long wordId) {
        List<Integer> quoteInfo = wordManager.getQuoteInfo(wordId);
        return success(quoteInfo);
    }

    /**
     * 导出
     * @param exportReqVO 导出参数
     */
    @PostMapping("/export")
    @Operation(summary = "导出汉语词典基础数据")
    @PreAuthorize("@ss.hasPermission('edu:word:export')")
    @LogRecord(type = LogRecordType.WORD_TYPE, bizNo = "{{#taskId}}", success = "创建汉语词典基础数据异步导出任务")
    public CommonResult<Long> exportWord(@Valid @RequestBody WordExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.WORD, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建汉语词典基础数据导出任务失败", e);
            return CommonResult.error(500, "创建汉语词典基础数据导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 获取专项引用
     * @param wordId 词条id
     */
    @PostMapping("/getSpecialExerciseQuote")
    public CommonResult<List<SpecialExercisePageRespDTO>> getQuestionInfoListByWordId(@RequestParam("wordId") Long wordId) {
        return CommonResult.success(specialExerciseApi.getQuestionInfoListByWordId(wordId));
    }

    /**
     * 获取互动课引用
     * @param wordId 词条id
     */
    @PostMapping("/getInteractiveCourseQuote")
    public CommonResult<List<InteractiveCourseUnitQuoteVO>> getInteractiveCourseQuoteByWordId(@RequestParam("wordId") Long wordId) {
        return CommonResult.success(interactiveCourseService.getInteractiveCourseQuoteByWordId(wordId));
    }


    /**
     * 查看越南语在专项练习中的引用信息
     * @param reqDTO 请求参数
     */
    @PostMapping("/getSpecialExerciseQuoteInfo")
    public CommonResult<SpecialExerciseOtQuoteDTO> getSpecialExerciseQuoteInfo(@RequestBody SpecialExerciseOtQuoteReqDTO reqDTO) {
        return CommonResult.success(wordManager.getSpecialExerciseQuoteInfo(reqDTO));
    }
}