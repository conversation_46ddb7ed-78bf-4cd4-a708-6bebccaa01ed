package com.xt.hsk.module.edu.controller.admin.word.vo;

import lombok.*;

import java.time.LocalDateTime;

@Data
public class WordExampleRespVO {


    /**
     * 例句唯一ID（自增）
     */
    private Long id;


    /**
     * 关联释义表word_meanings的meaning_id（如1）
     */
    private Long meaningId;


    /**
     * 冗余主表words的id（如38544）
     */
    private Long wordId;


    /**
     * 冗余主表的词语名称（如爱）
     */
    private String word;


    /**
     * 例句类型（如"e_cnen"）
     */
    private String exampleType;


    /**
     * 中文例句（如我很爱我的父母。）
     */
    private String translationCn;


    /**
     * 英文翻译（如I love my parents very much.）
     */
    private String translationEn;


    /**
     * 越南语例句（如我很爱我的父母。）
     */
    private String translationOt;


    /**
     * 带声调的拼音（如wǒ hěn ài wǒ de fùmǔ.）
     */
    private String pinyinWithTone;


    /**
     * 无声调的拼音（如wo hen ai wo de fumu.）
     */
    private String pinyinWithoutTone;


    /**
     * 关联音频文件ID（如153470）
     */
    private Integer audioId;


    /**
     * 关联音频文件url
     */
    private String audioUrl;


    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    /**
     * 排序
     */
    private Integer sort;


}