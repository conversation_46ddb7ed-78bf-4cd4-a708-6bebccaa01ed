package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

/**
 * 模考组卷规则 基本信息响应 VO
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class ExamPaperRuleBaseInfoRespVO {

    /**
     * 组卷规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;

} 