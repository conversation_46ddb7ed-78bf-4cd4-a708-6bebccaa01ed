package com.xt.hsk.module.edu.controller.app.exam;

import cn.dev33.satoken.annotation.SaIgnore;
import com.fhs.core.trans.anno.TransMethodResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.app.exam.vo.*;
import com.xt.hsk.module.edu.manager.exam.app.ExamCorrectionManager;
import com.xt.hsk.module.edu.manager.exam.app.ExamAppManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 模考 app 控制器
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Validated
@RestController
@RequestMapping("/edu/exam")
public class ExamAppController {

    @Resource
    private ExamAppManager examAppManager;

    @Resource
    private ExamCorrectionManager examCorrectionManager;

    /**
     * 分页获取模考
     */
    @SaIgnore
    @PostMapping("/v1/page")
    public CommonResult<PageResult<ExamAppPageRespVO>> getAppExamPage(@Valid @RequestBody ExamAppPageReqVO pageReqVO) {
        return success(examAppManager.getAppExamPage(pageReqVO));
    }

    /**
     * 获取模考元信息
     */
    @PostMapping("/v1/metadata")
    public CommonResult<ExamMetadataRespVO> getAppExamMetadata(@RequestBody ExamAppReqVO pageReqVO) {
        return success(examAppManager.getAppExamMetadata(pageReqVO));
    }

    /**
     * 开始模考
     */
    @PostMapping("/v1/start")
    public CommonResult<UserExamRecordAppRespVO> startExam(@RequestBody ExamAppReqVO reqVO) {
        return CommonResult.success(examAppManager.startExam(reqVO));
    }

    /**
     * 获取模考答题卡
     */
    @PostMapping("/v1/answer-card")
    public CommonResult<List<ExamAnswerCardRespVO>> getExamAnswerCard(@RequestBody ExamAnswerCardReqVO reqVO) {
        return CommonResult.success(examAppManager.getExamAnswerCard(reqVO));
    }

    /**
     * 获取模考报告答题卡
     */
    @PostMapping("/v1/report/answer-card")
    public CommonResult<List<ExamReportAnswerCardRespVO>> getExamReportAnswerCard(@RequestBody ExamReportAnswerCardReqVO reqVO) {
        return CommonResult.success(examAppManager.getExamReportAnswerCard(reqVO));
    }

    /**
     * 提交科目
     */
    @PostMapping("/v1/subject/submit")
    public CommonResult<Boolean> submitExamSubject(@RequestBody ExamSubjectSubmitReqVO reqVO) {
        examAppManager.submitExamSubject(reqVO);
        return success(true);
    }

    /**
     * 获取试听音频
     */
    @PostMapping("/v1/preview-audio")
    public CommonResult<ExamPreviewAudioRespVO> getPreviewAudio(@RequestBody ExamPreviewAudioReqVO reqVO) {
        return success(examAppManager.getPreviewAudio(reqVO));
    }

    /**
     * 提交模考
     */
    @PostMapping("/v1/submit")
    public CommonResult<Boolean> submitExam(@RequestBody ExamSubjectSubmitReqVO reqVO) {
        examAppManager.submitExam(reqVO);
        return success(true);
    }

    /**
     * 获取模考分科目进度信息
     */
    @PostMapping("/v1/sections-progress")
    public CommonResult<ExamSectionsProgressRespVO> getExamSectionsProgress(@RequestBody ExamSectionsProgressReqVO reqVO) {
        return success(examAppManager.getExamSectionsProgress(reqVO));
    }

    /**
     * 个人中心模考记录
     */
    @PostMapping("/v1/my-exam-record")
    @TransMethodResult
    public CommonResult<PageResult<UserExamRecordAppRespVO>> getMyExamRecord(@RequestBody ExamAppPageReqVO reqVO) {
        return CommonResult.success(examAppManager.getMyExamRecord(reqVO));
    }

    /**
     * 获取用户剩余模考次数
     */
    @PostMapping("/v1/remaining-count")
    public CommonResult<UserExamRemainingCountRespVO> getUserExamRemainingCount() {
        return CommonResult.success(examAppManager.getUserExamRemainingCount());
    }

    /**
     * 模考ai批改
     */
    @PostMapping("/v1/ai-correction")
    public CommonResult<Boolean> aiCorrection(@RequestBody ExamSubjectSubmitReqVO reqVO) {
        examCorrectionManager.aiCorrection(reqVO.getExamRecordId());
        return success(true);
    }

}