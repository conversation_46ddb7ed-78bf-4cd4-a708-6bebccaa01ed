package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模考详情 Resp Vo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class ExamDetailRespVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模考id
     */
    private Long examId;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;

    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;

    /**
     * 题型id列表
     */
    private String questionTypeIds;

    /**
     * 题型名称列表
     */
    private String questionNames;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 题目信息
     */
    private String questions;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}