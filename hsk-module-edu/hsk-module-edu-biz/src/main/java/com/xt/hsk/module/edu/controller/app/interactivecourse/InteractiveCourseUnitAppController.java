package com.xt.hsk.module.edu.controller.app.interactivecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseUnitReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppDetailsVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.InteractiveCourseUnitAppRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.NextUnitInfoRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.UpdateRecordSaveOptionReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.VideoRecordSaveVO;
import com.xt.hsk.module.edu.manager.interactivecourse.app.AppInteractiveCourseUnitManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP - 互动课单元接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@RestController
@RequestMapping("/edu/interactive-course-unit")
@Validated
@Slf4j
public class InteractiveCourseUnitAppController {

    @Resource
    private AppInteractiveCourseUnitManager appInteractiveCourseUnitManager;

    /**
     * 单元列表
     */
    @SaIgnore
    @PostMapping("/v1/page")
    public CommonResult<PageResult<InteractiveCourseUnitAppRespVO>> getInteractiveCourseUnitPage(
        @RequestBody @Valid AppInteractiveCourseUnitPageReqVO reqVO) {
        return success(appInteractiveCourseUnitManager.getInteractiveCourseUnitPage(reqVO));
    }

    /**
     * 单元详情 只有题目类型是视频的单元才会有视频详情
     */
    @PostMapping("/v1/get")
    public CommonResult<InteractiveCourseUnitAppDetailsVO> getInteractiveCourseUnitDetail(
        @RequestBody @Valid AppInteractiveCourseUnitReqVO reqVO) {
        return success(appInteractiveCourseUnitManager.getInteractiveCourseUnitDetail(reqVO));
    }

    /**
     * 记录视频观看记录
     */
    @PostMapping("/v1/videoRecord")
    public CommonResult<Boolean> recordVideoWatch(
        @RequestBody @Valid VideoRecordSaveVO reqVO) {
        appInteractiveCourseUnitManager.recordVideoWatch(reqVO);
        return success(true);
    }

    /**
     * 获取下一个单元信息
     */
    @PostMapping("/v1/getNextUnitInfo")
    public CommonResult<NextUnitInfoRespVO> getNextUnitInfo(
        @RequestBody @Valid AppInteractiveCourseUnitReqVO reqVO) {
        return success(appInteractiveCourseUnitManager.getNextUnitInfo(reqVO));
    }

    /**
     * 获取单元练习信息
     */
    @PostMapping("/v1/getCurrentUnitUnitInfo")
    public CommonResult<NextUnitInfoRespVO> getQuestionInfoList(
        @RequestBody @Valid AppInteractiveCourseUnitReqVO reqVO) {
        return success(appInteractiveCourseUnitManager.getCurrentUnitUnitInfo(reqVO));
    }

    /**
     * 更新记录保存选项
     */
    @PostMapping("/v1/updateRecordSaveOption")
    public CommonResult<Boolean> updateRecordSaveOption(
        @RequestBody @Valid UpdateRecordSaveOptionReqVO reqVO) {
        appInteractiveCourseUnitManager.updateRecordSaveOption(reqVO);
        return success(true);
    }

}
