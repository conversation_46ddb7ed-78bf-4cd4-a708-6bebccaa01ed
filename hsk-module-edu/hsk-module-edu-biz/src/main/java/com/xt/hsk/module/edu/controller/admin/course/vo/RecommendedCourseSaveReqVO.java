package com.xt.hsk.module.edu.controller.admin.course.vo;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * 管理后台 - 推荐课程创建 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@ToString(callSuper = true)
public class RecommendedCourseSaveReqVO {

    /**
     * 课程ID
     */
    @NotNull(message = "课程ID不能为空")
    private List<Long> courseIds;

} 