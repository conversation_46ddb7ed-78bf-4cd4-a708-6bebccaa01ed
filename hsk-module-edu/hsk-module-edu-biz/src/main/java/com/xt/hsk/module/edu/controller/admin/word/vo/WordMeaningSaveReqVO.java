package com.xt.hsk.module.edu.controller.admin.word.vo;

import lombok.*;
import jakarta.validation.constraints.*;

import java.util.List;

@Data
public class WordMeaningSaveReqVO {


    /**
     * 冗余主表words的id（如38544）
     */
    @NotNull(message = "冗余主表words的id（如38544）不能为空")
    private Long wordId;

    /**
     * 冗余主表的词语名称（如爱）
     */
    @NotEmpty(message = "冗余主表的词语名称（如爱）不能为空")
    private String word;

    /**
     * 词性分类（如v,n，v-动词，n-名词）
     */
    private Integer kind;
    /**
     * 词性描述
     */
    private String kindDesc;
    /**
     * 词性分类（如v,n，v-动词，n-名词）
     */
    private List<String> kinds;

    /**
     * 越南语
     */
    private String translationOt;
    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;
    /**
     * 英文释义（如love）
     */
    @NotEmpty(message = "英文释义（如love）不能为空")
    private String translationEn;

    /**
     * 中文解释（这儿放的是中文解释，非单词如对人或事物有很深的感情）
     */
    private String interpretation;

    /**
     * 例句
     */
    private List<WordExampleSaveReqVO> examples;
    /**
     * 拼音（如ài）
     */
    private String pinyin;

    /**
     * 排序
     */
    private Integer sort;
}