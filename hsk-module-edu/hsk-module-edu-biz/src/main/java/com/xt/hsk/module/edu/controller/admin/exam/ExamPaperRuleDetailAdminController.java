package com.xt.hsk.module.edu.controller.admin.exam;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleEditReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleEditSubjectRespVO;
import com.xt.hsk.module.edu.manager.exam.admin.ExamPaperRuleDetailAdminManager;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 模考组卷规则明细 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Validated
@RestController
@RequestMapping("/edu/exam-paper-rule-detail")
public class ExamPaperRuleDetailAdminController {

    @Resource
    private ExamPaperRuleDetailAdminManager examPaperRuleDetailAdminManager;

    /**
     * 获取组卷规则编辑数据
     */
    @PostMapping("/for-edit")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule-detail:query')")
    public CommonResult<ExamPaperRuleEditSubjectRespVO> getExamPaperRuleForEdit(@RequestBody ExamPaperRuleEditReqVO reqVO) {
        return success(examPaperRuleDetailAdminManager.getExamPaperRuleForEdit(reqVO));
    }


}