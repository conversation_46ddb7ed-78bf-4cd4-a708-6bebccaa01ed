package com.xt.hsk.module.edu.controller.app.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.LearningValidityPeriodEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户中心课程VO
 */
@Data
public class UserCenterCourseVo implements Serializable {
    /**
     * 课程ID
     */
    private Long courseId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;
    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;
    /**
     * 课时数
     */
    private Integer classHourCount;
    /**
     * 学习有效期 1：长期有效 2：按截止日期 3：按天数
     *
     * @see LearningValidityPeriodEnum
     */
    private Integer learningValidityPeriod;
    /**
     * 学习有效期
     */
    private LocalDateTime endTime;
    /**
     * 课程登记id
     */
    private Long courseRegisterId;
    /**
     * 是否已过期
     */
    private Boolean isExpired = false;

}
