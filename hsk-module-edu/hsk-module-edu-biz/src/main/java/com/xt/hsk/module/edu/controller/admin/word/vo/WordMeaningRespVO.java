package com.xt.hsk.module.edu.controller.admin.word.vo;

import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WordMeaningRespVO {


    /**
     * 例句唯一ID（自增）
     */
    private Long id;


    /**
     * 冗余主表words的id（如38544）
     */
    private Long wordId;


    /**
     * 冗余主表的词语名称（如爱）
     */
    private String word;


    /**
     * 英文释义（如love）
     */
    private String translationEn;


    /**
     * 中文解释（这儿放的是中文解释，非单词如对人或事物有很深的感情）
     */
    private String interpretation;
    /**
     * 拼音（如ài）
     */
    private String pinyin;


    /**
     * 越南语
     */
    private String translationOt;


    /**
     * 词性分类（如v,n，v-动词，n-名词）
     */
    private Integer kind;
    /**
     * 词性分类（如v,n，v-动词，n-名词）
     */
    private List<String> kinds;
    /**
     * 词性描述
     */
    private String kindDesc;


    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;


    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * 例句
     */
    private List<WordExampleRespVO> examples;

    /**
     * 排序
     */
    private Integer sort;
}