package com.xt.hsk.module.edu.controller.admin.word.vo;

import lombok.*;
import jakarta.validation.constraints.*;

@Data
public class WordExampleSaveReqVO {


    /**
     * 关联释义表word_meanings的meaning_id（如1）
     */
    @NotNull(message = "关联释义表word_meanings的meaning_id（如1）不能为空")
    private Long meaningId;

    /**
     * 冗余主表words的id（如38544）
     */
    @NotNull(message = "冗余主表words的id（如38544）不能为空")
    private Long wordId;

    /**
     * 冗余主表的词语名称（如爱）
     */
    @NotEmpty(message = "冗余主表的词语名称（如爱）不能为空")
    private String word;

    /**
     * 例句类型（如"e_cnen"）
     */
    @NotEmpty(message = "例句类型（如'e_cnen'）不能为空")
    private String exampleType;

    /**
     * 中文例句（如我很爱我的父母。）
     */
    private String translationCn;

    /**
     * 英文翻译（如I love my parents very much.）
     */
    private String translationEn;

    /**
     * 越南语例句（如我很爱我的父母。）
     */
    private String translationOt;

    /**
     * 带声调的拼音（如wǒ hěn ài wǒ de fùmǔ.）
     */
    private String pinyinWithTone;

    /**
     * 无声调的拼音（如wo hen ai wo de fumu.）
     */
    private String pinyinWithoutTone;

    /**
     * 关联音频文件url
     */
    private String audioUrl;
    /**
     * 排序
     */
    private Integer sort;

}