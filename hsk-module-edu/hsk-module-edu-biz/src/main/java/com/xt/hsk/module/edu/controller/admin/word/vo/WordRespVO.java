package com.xt.hsk.module.edu.controller.admin.word.vo;

import com.fhs.core.trans.anno.IgnoreTrans;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagRespVO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
public class WordRespVO implements VO {


    /**
     * 例句唯一ID（自增）
     */
    private Long id;


    /**
     * 数据类型（如cnen表示汉英）
     */
    private String type;


    /**
     * 汉字/词语（如爱）
     */
    private String word;


    /**
     * 拼音（如ài）
     */
    private String pinyin;


    /**
     * 音标（如ai）
     */
    private String phonetic;


    /**
     * 复合词（多个词语用分号分隔，如爱不忍释; 爱不释手）
     */
    private String compound;


    /**
     * 反义词数组（如[恨, 恶, 憎]）
     */
    private String antonyms;


    /**
     * 同义词数组（如[热爱, 喜爱]）
     */
    private String synonyms;


    /**
     * 注音符号（如ㄞˋ）
     */
    private String zhuyin;


    /**
     * 音频文件ID
     */
    private Integer audioId;


    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;


    /**
     * 音频文件URL
     */
    private String audioUrl;


    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;

    /**
     * HSK等级
     */
    private List<Integer> hskLevels;
    /**
     * 等级描述
     */
    private String hskLevelsDesc;
    /**
     * 等级描述Vo
     */
    @IgnoreTrans
    private List<HskEnum> hskVos;

    /**
     * 词性
     */
    private String kindsDesc;
    /**
     * 标签
     */
    private List<Long> tags;
    /**
     * 标签描述
     */
    private String tagsDesc;
    /**
     * 标签描述
     */
    private List<TagRespVO> tagRespVOS;
    /**
     * 最近更新人
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private Long updater;
    /**
     * 最近更新人名称
     */
    private String updaterName;
    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 收藏量
     */
    private Integer collectCount;
    /**
     * 引用量
     */
    private Integer quoteCount;
    /**
     * hsk 等级
     */
    private Integer hskLevel;
    /**
     * 含义
     */
    private List<WordMeaningRespVO> meanings;
    /**
     * 去重后的越南语
     */
    private Set<String> translationOts;
}