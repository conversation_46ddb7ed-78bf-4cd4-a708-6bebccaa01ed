package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import lombok.Data;

/**
 * 互动课单元上次视频观看信息
 *
 * <AUTHOR>
 * @since 2025/07/07
 */
@Data
public class LastVideoRecordInfoVO {
    /**
     * 视频记录id
     */
    private Long videoRecordId;
    /**
     * 是否是用户在课程下最新记录 1-是 0-否
     */
    private Integer isLatest;
    /**
     * 视频总时间长(单位秒)
     */
    private Integer videoDuration;
    /**
     * 用户观看总时长(单位秒)
     */
    private Integer viewingDuration;
    /**
     * 视频观看进度百分比 viewing_duration / video_duration * 100
     */
    private Integer videoProgress;
    /**
     * 记录保存选项 1-不保存 2-保存
     * 如果选择不保存，下次进入时强制继续练习，不提示重新开始
     * @see com.xt.hsk.module.edu.enums.interactivecourse.RecordSaveOptionEnum
     */
    private Integer saveOption;
}
