package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 模考 save req vo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class ExamSaveReqVO {

    /**
     * 模考ID
     */
    private Long id;

    /**
     * HSK等级
     */
    @InEnum(value = HskEnum.class, message = "HSK等级值必须是 {value} 范围内")
    @NotNull(message = "请选择HSK等级")
    private Integer hskLevel;

    /**
     * 模考组卷规则id
     */
    @NotNull(message = "请选择模考组卷规则")
    private Long paperRuleId;

    /**
     * 模考名称
     */
    @NotEmpty(message = "请输入模考名称")
    private String name;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    // @InEnum(value = ExamTypeEnum.class, message = "模考类型必须是 {value} 范围内")
    // @NotNull(message = "请选择模考类型")
    private Integer type;

    /**
     * 模考封面图片URL
     */
    private String coverUrl;

    /**
     * 模考描述
     */
    private String description;

    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 已参加考试人数
     */
    private Integer examCount;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 发布状态：0-未发布 1-已发布 2-已下架
     *
     * @see ExamPublishStatusEnum
     */
    private Integer publishStatus;

    /**
     * 考试详情科目列表
     */
    private List<ExamDetailSubjectReqVO> examDetailSubjectList;

}