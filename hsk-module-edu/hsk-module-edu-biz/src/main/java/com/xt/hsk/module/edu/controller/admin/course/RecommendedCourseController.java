package com.xt.hsk.module.edu.controller.admin.course;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCoursePageReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseRespVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.course.vo.RecommendedCourseUpdateSortReqVO;
import com.xt.hsk.module.edu.manager.course.RecommendedCourseManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 管理后台 - 课程推荐
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@RestController
@RequestMapping("/edu/recommended-course")
@Validated
public class RecommendedCourseController {

    @Resource
    private RecommendedCourseManager recommendedCourseManager;

    /**
     * 创建课程推荐
     *
     * @param createReqVO 创建信息
     * @return 创建的课程推荐ID列表
     */
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('edu:recommended-course:create')")
    public CommonResult<List<Long>> createRecommendedCourse(
        @Valid @RequestBody RecommendedCourseSaveReqVO createReqVO) {
        return success(recommendedCourseManager.createRecommendedCourse(createReqVO));
    }

    /**
     * 更新课程推荐排序
     *
     * @param updateSortReqVO 更新排序信息
     * @return 是否成功
     */
    @PutMapping("/update-sort")
    @PreAuthorize("@ss.hasPermission('edu:recommended-course:update')")
    public CommonResult<Boolean> updateRecommendedCourseSort(
        @Valid @RequestBody RecommendedCourseUpdateSortReqVO updateSortReqVO) {
        recommendedCourseManager.updateRecommendedCourseSort(updateSortReqVO);
        return success(true);
    }

    /**
     * 删除课程推荐
     *
     * @param id 编号
     * @return 是否成功
     */
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('edu:recommended-course:delete')")
    public CommonResult<Boolean> deleteRecommendedCourse(@RequestParam("id") Long id) {
        recommendedCourseManager.deleteRecommendedCourse(id);
        return success(true);
    }

    /**
     * 获得课程推荐分页
     *
     * @param pageVO 分页查询参数
     * @return 课程推荐分页列表
     */
    @PostMapping("/page")
    @PreAuthorize("@ss.hasPermission('edu:recommended-course:query')")
    public CommonResult<PageResult<RecommendedCourseRespVO>> getRecommendedCoursePage(
        @Valid @RequestBody RecommendedCoursePageReqVO pageVO) {
        return success(recommendedCourseManager.getRecommendedCoursePage(pageVO));
    }
} 