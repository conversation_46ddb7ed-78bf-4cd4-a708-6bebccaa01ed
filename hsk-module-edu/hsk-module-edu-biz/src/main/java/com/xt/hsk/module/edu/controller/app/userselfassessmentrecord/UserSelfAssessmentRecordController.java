package com.xt.hsk.module.edu.controller.app.userselfassessmentrecord;

import cn.dev33.satoken.stp.StpUtil;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.QuestionBankVo;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordRespVO;
import com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo.UserSelfAssessmentRecordSaveReqVO;
import com.xt.hsk.module.edu.service.userselfassessmentrecord.UserSelfAssessmentRecordManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

@Tag(name = "App - 用户等级自测记录")
@RestController
@RequestMapping("/edu/user-self-assessment-record")
public class UserSelfAssessmentRecordController {

    @Resource
    private UserSelfAssessmentRecordManager userSelfAssessmentRecordManager;

    @PostMapping("/start")
    @Operation(summary = "用户等级自测开始")
    public CommonResult<UserSelfAssessmentRecordRespVO> createUserSelfAssessmentRecord() {
        long userId = StpUtil.getLoginIdAsLong();
        return success(userSelfAssessmentRecordManager.createUserSelfAssessmentRecord(userId));
    }

    @PostMapping("/submit")
    @Operation(summary = "用户等级自测结束")
    public CommonResult<Boolean> submitUserSelfAssessmentRecord(@RequestBody UserSelfAssessmentRecordSaveReqVO updateReqVO) {
        userSelfAssessmentRecordManager.submitUserSelfAssessmentRecord(updateReqVO);
        return success(true);
    }

    @PostMapping("/setQuestions")
    @Operation(summary = "手动设置题目")
    public CommonResult<Boolean> setQuestions(@RequestBody List<QuestionBankVo> questionBankVos) {
        userSelfAssessmentRecordManager.setQuestions(questionBankVos);
        return success(true);
    }
}