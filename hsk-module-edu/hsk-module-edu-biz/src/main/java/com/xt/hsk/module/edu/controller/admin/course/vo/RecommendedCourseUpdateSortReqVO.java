package com.xt.hsk.module.edu.controller.admin.course.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
 * 管理后台 - 推荐课程更新排序 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@ToString(callSuper = true)
public class RecommendedCourseUpdateSortReqVO {

    /**
     * 推荐ID
     */
    @NotNull(message = "推荐ID不能为空")
    private Long id;

    /**
     * 排序序号
     */
    @NotNull(message = "排序序号不能为空")
    private Integer sort;

} 