package com.xt.hsk.module.edu.controller.app.home.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首页 信息 VO
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HomeInfoVO {

    /**
     * 上次学习课程信息
     */
    private LastLearnedCourseRespVO lastLearnedCourse;

    /**
     * 专项练习可用性信息
     */
    private SpecialExerciseAvailabilityVO specialExerciseAvailability;

    /**
     * 是否存在题目信息
     */
    private Boolean haveQuestions;

    /**
     * 考试可用性列表
     */
    private List<ExamAvailabilityVO> examAvailabilityList;
}
