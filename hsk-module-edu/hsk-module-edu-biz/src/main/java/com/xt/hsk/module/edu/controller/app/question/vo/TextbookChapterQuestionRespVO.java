package com.xt.hsk.module.edu.controller.app.question.vo;

import lombok.Data;

@Data
public class TextbookChapterQuestionRespVO {
    /**
     * 练习记录id
     */
    private Long practiceRecordId;
    /**
     * 教材id
     */
    private Long textbookId;
//    /**
//     * 教材名
//     */
//    private String textbookName;

    /**
     * 章节ID
     */
    private Long chapterId;
//    /**
//     * 章节名
//     */
//    private String chapterName;
    /**
     * 题目数量
     */
    private Integer questionCount;
    /**
     * 正确数量
     */
    private Integer correctCount;
    /**
     * 练习数量
     */
    private Integer exerciseCount;
}
