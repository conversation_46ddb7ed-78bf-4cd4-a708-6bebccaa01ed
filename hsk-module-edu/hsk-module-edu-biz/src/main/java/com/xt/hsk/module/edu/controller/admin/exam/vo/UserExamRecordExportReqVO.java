package com.xt.hsk.module.edu.controller.admin.exam.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 用户模考记录导出请求参数
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserExamRecordExportReqVO extends UserExamRecordPageReqVO {

    /**
     * 导出任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

} 