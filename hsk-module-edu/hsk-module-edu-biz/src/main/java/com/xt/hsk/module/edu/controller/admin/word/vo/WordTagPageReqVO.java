package com.xt.hsk.module.edu.controller.admin.word.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WordTagPageReqVO extends PageParam {

    /**
     * words的id
     */
    private Long wordId;

    /**
     * 汉字/词语 冗余一部分字词的信息，避开连表
     */
    private String word;

    /**
     * 拼音（如ài）
     */
    private String pinyin;

    /**
     * 标签id）
     */
    private Long tagId;
    /**
     * 记录创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}