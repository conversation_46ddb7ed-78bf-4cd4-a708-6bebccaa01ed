package com.xt.hsk.module.edu.controller.admin.question.questionversion;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionRespVO;
import com.xt.hsk.module.edu.controller.admin.question.questionversion.vo.QuestionVersionSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questionversion.QuestionVersionDO;
import com.xt.hsk.module.edu.service.question.questionversion.QuestionVersionManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 题目表版本库")
@RestController
@RequestMapping("/edu/question-version")
@Validated
public class QuestionVersionController {

    @Resource
    private QuestionVersionManager questionVersionManager;

    @PostMapping("/create")
    @Operation(summary = "创建题目表版本库")
    @PreAuthorize("@ss.hasPermission('edu:question-version:create')")
    public CommonResult<Long> createQuestionVersion(@Valid @RequestBody QuestionVersionSaveReqVO createReqVO) {
        return success(questionVersionManager.createQuestionVersion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目表版本库")
    @PreAuthorize("@ss.hasPermission('edu:question-version:update')")
    public CommonResult<Boolean> updateQuestionVersion(@Valid @RequestBody QuestionVersionSaveReqVO updateReqVO) {
        questionVersionManager.updateQuestionVersion(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题目表版本库")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-version:delete')")
    public CommonResult<Boolean> deleteQuestionVersion(@RequestParam("id") Long id) {
        questionVersionManager.deleteQuestionVersion(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得题目表版本库")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-version:query')")
    public CommonResult<QuestionVersionRespVO> getQuestionVersion(@RequestParam("id") Long id) {
        QuestionVersionDO questionVersion = questionVersionManager.getQuestionVersion(id);
        return success(BeanUtils.toBean(questionVersion, QuestionVersionRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得题目表版本库分页")
    @PreAuthorize("@ss.hasPermission('edu:question-version:query')")
    public CommonResult
            <PageResult
                    <QuestionVersionRespVO>> getQuestionVersionPage(@Valid
                                                                    @RequestBody QuestionVersionPageReqVO pageReqVO) {
        PageResult<QuestionVersionDO> pageResult = questionVersionManager.getQuestionVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionVersionRespVO.class));
    }


}