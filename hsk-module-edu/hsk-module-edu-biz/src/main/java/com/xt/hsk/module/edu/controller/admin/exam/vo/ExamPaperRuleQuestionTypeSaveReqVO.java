package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考组卷规则题型 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRuleQuestionTypeSaveReqVO {

    /**
     * 组卷规则明细ID
     */
    private Long id;

    /**
     * 组卷规则ID
     */
    private Long paperRuleId;

    /**
     * 模考题型ID
     */
    @NotNull(message = "模考题型ID不能为空")
    private Long examQuestionTypeId;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    @InEnum(value = SubjectEnum.class, message = "科目值必须是 {value} 范围内")
    @NotNull(message = "科目不能为空")
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    @InEnum(value = ExamQuestionTypeUnitEnum.class, message = "单元值必须是 {value} 范围内")
    @NotNull(message = "单元部分不能为空")
    private Integer unit;

    /**
     * 题数
     */
    @Min(value = 0, message = "题目数量不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "题目数量超出允许的最大值")
    @NotNull(message = "题目数量不能为空")
    private Integer questionCount;

}