package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CourseVideoLinkVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CoursewareInfoVO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * 互动课单元 Resp VO
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@Data
public class InteractiveCourseUnitRespVO implements VO {

    /**
     * 课程单元ID
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 单元名称-中文
     */
    private String unitNameCn;

    /**
     * 单元名称-英文
     */
    private String unitNameEn;

    /**
     * 单元名称-其他
     */
    private String unitNameOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * HSK等级（1-6）
     */
    private Integer hskLevel;

    /**
     * 单元类型 1-视频 2-专项练习 3-真题练习
     *
     * @see UnitQuestionSourceTypeEnum
     */
    private Integer questionSource;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 推荐学习时长(秒)
     */
    private Integer recommendedDuration;

    /**
     * 视频信息（当unitType=1时）
     */
    private VideoInfoVO videoInfo;
    /**
     * 视频 ID
     */
    private String videoId;

    /**
     * 视频类型 1-视频带课件 2-视频带生词
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum
     */
    private Integer videoType;

    /**
     * 视频尺寸比例 1-9:16 2-16:9
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoAspectRatioEnum
     */
    private Integer aspectRatio;

    /**
     * 视频名称-中文
     */
    private String videoNameCn;

    /**
     * 视频名称-英文
     */
    private String videoNameEn;

    /**
     * 视频名称-其他
     */
    private String videoNameOt;

    /**
     * 专项练习信息（当unitType=2时）
     */
    private UnitSpecialPracticeInfoVO specialPracticeInfo;


    private List<UnitQuestionInfoVO> questionList;

    /**
     * 课件列表（可选，当视频类型=1时）
     */
    private List<CoursewareInfoVO> coursewareList;

    /**
     * 生词列表（可选，当视频类型=2时）
     */
    private List<UnitVocabularyInfoVO> vocabularyList;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;
    
    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 题目类型描述
     * 如果是专项练习 就是专项练习类型 如果是真题就是题目类型 如果是本地视频 就是视频类型
     */
    private String unitQuestionTypeDesc;

    /**
     * 生词ID信息
     */
    private List<VocabularyInfoVO> wordIdList;
    /**
     * 专项练习ID信息
     */
    private List<SpecialPracticeInfoVO> specialPracticeIdList;
    /**
     * 真题ID信息
     */
    private List<QuestionInfoVO> questionIdList;

    /**
     * 视频信息VO
     */
    @Data
    public static class VideoInfoVO {

        /**
         * 视频名称-中文
         */
        private String videoNameCn;

        /**
         * 视频名称-英文
         */
        private String videoNameEn;

        /**
         * 视频名称-其他
         */
        private String videoNameOt;

        /**
         * 视频类型 1-视频带课件 2-视频带生词
         *
         * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum
         */
        private Integer videoType;

        /**
         * 视频尺寸比例 1-9:16 2-16:9
         *
         * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoAspectRatioEnum
         */
        private Integer aspectRatio;

        /**
         * 视频时长，单位：秒
         */
        private Long duration;

        /**
         * 视频链接列表
         */
        private List<CourseVideoLinkVO> videoLinkList;
    }

    /**
     * 生词ID信息
     */
    @Data
    public static class VocabularyInfoVO {

        /**
         * 字词ID
         */
        private Long wordId;

        /**
         * 序号
         */
        private Integer sort;

    }

    /**
     * 专项练习信息ID
     */
    @Data
    public static class SpecialPracticeInfoVO {

        /**
         * 练习组ID
         */
        private Long practiceGroupId;

        /**
         * 序号
         */
        private Integer sort;

    }

    /**
     * 真题练习信息ID
     */
    @Data
    public static class QuestionInfoVO{

        /**
         * 真题练习ID
         */
        private Long questionId;

        /**
         * 序号
         */
        private Integer sort;
    }
}