package com.xt.hsk.module.edu.controller.admin.teacher.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 讲师分页查询参数
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherPageReqVO extends PageParam {

    /**
     * 讲师名称-中文
     */
    private String teacherNameCn;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;

    /**
     * 选中的讲师ID列表，用于导出功能
     */
    private List<Long> ids;

} 