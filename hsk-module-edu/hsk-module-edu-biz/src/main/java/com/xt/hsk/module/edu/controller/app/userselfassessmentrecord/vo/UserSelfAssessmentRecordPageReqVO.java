package com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserSelfAssessmentRecordPageReqVO extends PageParam {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 已作答数量
     */
    private Integer answerNum;

    /**
     * 已正确数量
     */
    private Integer correctNum;

    /**
     * 题目数量
     */
    private Integer questionNum;

    /**
     * 开始作答时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    /**
     * 结束作答时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;

    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;

    /**
     * 预测水平 hskLevel 1 2 4 8 16 32
     */
    private Integer predictionLevel;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}