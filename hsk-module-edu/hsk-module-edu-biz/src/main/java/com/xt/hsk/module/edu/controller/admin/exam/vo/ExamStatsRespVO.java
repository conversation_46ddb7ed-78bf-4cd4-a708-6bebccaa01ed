package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

@Data
public class ExamStatsRespVO {

    /**
     * 模考ID
     */
    private Long examId;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;

    /**
     * 参考人数
     */
    private Long registeredCount;

    /**
     * 已考人数
     */
    private Long completedCount;
}
