package com.xt.hsk.module.edu.controller.admin.teacher.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 讲师响应 VO
 *
 * <AUTHOR>
 */
@Data
public class TeacherRespVO implements VO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 讲师名称-中文
     */
    private String teacherNameCn;

    /**
     * 讲师名称-英文
     */
    private String teacherNameEn;

    /**
     * 讲师名称-其他
     */
    private String teacherNameOt;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 手机区号
     */
    private String countryCode;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 讲师介绍-中文
     */
    private String teacherIntroCn;

    /**
     * 讲师介绍-英文
     */
    private String teacherIntroEn;

    /**
     * 讲师介绍-其他
     */
    private String teacherIntroOt;

    /**
     * 营销语-中文
     */
    private String marketingSloganCn;

    /**
     * 营销语-英文
     */
    private String marketingSloganEn;

    /**
     * 营销语-其他
     */
    private String marketingSloganOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    private Boolean displayStatus;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private String updater;

    /**
     * 更新人名称
     */
    private String updaterName;

    /**
     * 讲师关联的课程数量
     */
    private Integer courseCount;

} 