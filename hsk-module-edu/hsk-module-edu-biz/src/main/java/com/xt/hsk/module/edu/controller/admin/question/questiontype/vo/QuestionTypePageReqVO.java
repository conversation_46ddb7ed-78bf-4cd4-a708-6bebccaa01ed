package com.xt.hsk.module.edu.controller.admin.question.questiontype.vo;

import lombok.*;

import java.io.Serializable;
import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@ToString(callSuper = true)
public class QuestionTypePageReqVO extends PageParam {

    /**
     * 题型名称
     */
    private String nameCn;

    /**
     * 题型名称 英文
     */
    private String nameEn;

    /**
     * 题型名称 其他语言
     */
    private String nameOt;

    /**
     * 科目枚举值（1=听力，2=阅读，4=书写）
     */
    private Integer subject;

    /**
     * hsk等级 com.xt.hsk.framework.common.enums.HskEnum
     * 单选时用这个,多选自己建hskLevels 字段
     */
    private Integer hskLevel;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}