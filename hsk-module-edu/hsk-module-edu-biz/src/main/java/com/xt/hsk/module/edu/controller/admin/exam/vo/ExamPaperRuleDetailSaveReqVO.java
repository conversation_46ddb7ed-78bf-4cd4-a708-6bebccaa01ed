package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考组卷规则明细 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRuleDetailSaveReqVO {

    /**
     * 组卷规则明细ID
     */
    private Long id;

    /**
     * 组卷规则ID
     */
    private Long paperRuleId;

    /**
     * 模考题型ID
     */
    @NotNull(message = "模考题型ID不能为空")
    private Long examQuestionTypeId;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    @NotNull(message = "科目不能为空")
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    @NotNull(message = "单元部分不能为空")
    private Integer unit;

    /**
     * 题数
     */
    @Min(value = 0, message = "题目数量不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "题目数量超出允许的最大值")
    private Integer questionCount;

}