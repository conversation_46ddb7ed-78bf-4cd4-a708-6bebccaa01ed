package com.xt.hsk.module.edu.controller.admin.tag.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class TagSaveReqVO {

    /**
     * 唯一ID（自增）
     */
    private Long id;

    /**
     * 标签名
     */
    @NotEmpty(message = "标签名不能为空")
    private String tagName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

}