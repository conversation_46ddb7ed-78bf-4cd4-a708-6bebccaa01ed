package com.xt.hsk.module.edu.controller.app.question.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AppUserAnswerDetailSaveVO implements Serializable {
    /**
     * 作答数据id
     */
    private Long id;
    /**
     * 用户答案
     */
    private String userAnswer;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * 题目id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 版本
     */
    private Integer version;
}
