package com.xt.hsk.module.edu.controller.app.elitecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * APP - 精品课分页 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EliteCourseAppPageReqVO extends PageParam {

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 课程登记id
     */
    private Long courseRegisterId;
    /**
     * 课时id
     */
    private Long classHourId;

} 