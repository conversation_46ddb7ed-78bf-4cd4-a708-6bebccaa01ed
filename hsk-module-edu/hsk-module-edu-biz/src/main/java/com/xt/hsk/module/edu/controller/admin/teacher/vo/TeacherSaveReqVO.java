package com.xt.hsk.module.edu.controller.admin.teacher.vo;

import com.xt.hsk.framework.common.constants.RegexpConstant;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

/**
 * 讲师创建/更新参数类
 *
 * <AUTHOR>
 */
@Data
public class TeacherSaveReqVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 讲师名称-中文
     */
    @NotEmpty(message = "讲师名称不能为空")
    private String teacherNameCn;

    /**
     * 讲师名称-英文
     */
    private String teacherNameEn;

    /**
     * 讲师名称-其他
     */
    private String teacherNameOt;

    /**
     * 头像
     */
    @NotEmpty(message = "头像不能为空")
    private String avatar;

    /**
     * 手机区号 只能是数字
     */
    @NotEmpty(message = "手机区号不能为空")
    @Pattern(regexp = RegexpConstant.COUNTRY_CODE, message = "手机区号必须是1-3位数字")
    private String countryCode;

    /**
     * 手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = RegexpConstant.MOBILE, message = "手机号必须是7-11位数字")
    private String mobile;

    /**
     * 讲师介绍-中文
     */
    private String teacherIntroCn;

    /**
     * 讲师介绍-英文
     */
    private String teacherIntroEn;

    /**
     * 讲师介绍-其他
     */
    private String teacherIntroOt;

    /**
     * 营销语-中文
     */
    @NotEmpty(message = "营销语不能为空")
    private String marketingSloganCn;

    /**
     * 营销语-英文
     */
    private String marketingSloganEn;

    /**
     * 营销语-其他
     */
    private String marketingSloganOt;

    /**
     * 展示状态 0-隐藏 1-显示
     */
    @NotNull(message = "展示状态不能为空")
    private Boolean displayStatus;

} 