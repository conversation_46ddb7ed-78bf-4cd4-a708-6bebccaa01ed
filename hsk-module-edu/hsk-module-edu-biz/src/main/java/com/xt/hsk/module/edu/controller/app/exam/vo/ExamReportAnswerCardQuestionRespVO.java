package com.xt.hsk.module.edu.controller.app.exam.vo;

import lombok.Data;

/**
 * 模考报告答题卡题目响应 VO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Data
public class ExamReportAnswerCardQuestionRespVO {

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题目详情ID
     */
    private Long questionDetailId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 答题状态（1-未答，2-已答，3-答对，4-答错，5-未批改）
     * 书写题：1-未答，2-已答，5-未批改
     * 其他题型：1-未答，3-答对，4-答错
     */
    private Integer answerStatus;

}