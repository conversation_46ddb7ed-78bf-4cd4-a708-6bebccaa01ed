package com.xt.hsk.module.edu.controller.admin.course.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 管理后台 - 推荐课程分页 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RecommendedCoursePageReqVO extends PageParam {

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 课程名称
     */
    private String courseName;

} 