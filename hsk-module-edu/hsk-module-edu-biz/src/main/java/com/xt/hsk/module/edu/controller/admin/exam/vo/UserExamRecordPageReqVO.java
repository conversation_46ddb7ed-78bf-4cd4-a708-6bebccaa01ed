package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * 用户模考记录 page req vo
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserExamRecordPageReqVO extends PageParam {

    /**
     * 模考id
     */
    @NotNull(message = "请选择模考")
    private Long examId;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @InEnum(value = ExamTypeEnum.class, message = "模考类型值必须是 {value} 范围内")
    @NotNull(message = "请选择模考类型")
    private Integer examType;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    @InEnum(value = ExamSubjectSectionsEnum.class, message = "参与的模考科目值必须是 {value} 范围内")
    @NotNull(message = "请选择参与的模考科目")
    private Integer examSections;

    /**
     * 批改状态 1进行中 2待批改 3已批改
     *
     * @see ExamCorrectionStatusEnum
     */
    @InEnum(value = ExamCorrectionStatusEnum.class, message = "批改状态值必须是 {value} 范围内")
    private Integer correctionStatus;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * IDS
     */
    private List<Long> ids;

}