package com.xt.hsk.module.edu.controller.app.home.vo;

import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模考可用性 VO
 *
 * <AUTHOR>
 * @since 2025/07/29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExamAvailabilityVO {

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer type;

    /**
     * 是否有该类型的模考
     */
    private Boolean hasExams;
}
