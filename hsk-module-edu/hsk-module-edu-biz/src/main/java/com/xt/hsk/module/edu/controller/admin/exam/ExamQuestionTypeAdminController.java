package com.xt.hsk.module.edu.controller.admin.exam;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDeleteCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDuplicateCheckReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeDuplicateCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypePageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamQuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.ExamQuestionTypeDO;
import com.xt.hsk.module.edu.manager.exam.admin.ExamQuestionTypeAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模考题型 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Validated
@RestController
@RequestMapping("/edu/exam-question-type")
public class ExamQuestionTypeAdminController {

    @Resource
    private ExamQuestionTypeAdminManager examQuestionTypeAdminManager;

    /**
     * 创建模考题型
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.EXAM_QUESTION_TYPE,
        subType = "创建模考题型",
        bizNo = "{{#questionTypeId}}",
        success = "创建模考题型")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:create')")
    public CommonResult<Long> createExamQuestionType(@Valid @RequestBody ExamQuestionTypeSaveReqVO createReqVO) {
        return success(examQuestionTypeAdminManager.createExamQuestionType(createReqVO));
    }

    /**
     * 更新模考题型
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.EXAM_QUESTION_TYPE,
        subType = "修改模考题型",
        bizNo = "{{#updateReqVO.id}}",
        success = "创建模考题型")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:update')")
    public CommonResult<Boolean> updateExamQuestionType(@Valid @RequestBody ExamQuestionTypeSaveReqVO updateReqVO) {
        examQuestionTypeAdminManager.updateExamQuestionType(updateReqVO);
        return success(true);
    }

    /**
     * 删除模考题型
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.EXAM_QUESTION_TYPE,
        subType = "删除模考题型", bizNo = "{{#id}}",
        success = "删除模考题型")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:delete')")
    public CommonResult<Boolean> deleteExamQuestionType(@RequestParam("id") Long id) {
        examQuestionTypeAdminManager.deleteExamQuestionType(id);
        return success(true);
    }

    /**
     * 根据id获取模考题型
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:query')")
    public CommonResult<ExamQuestionTypeRespVO> getExamQuestionType(@RequestParam("id") Long id) {
        ExamQuestionTypeDO examQuestionType = examQuestionTypeAdminManager.getExamQuestionType(id);
        return success(BeanUtils.toBean(examQuestionType, ExamQuestionTypeRespVO.class));
    }

    /**
     * 分页获取模考题型
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:query')")
    public CommonResult<PageResult<ExamQuestionTypeRespVO>> getExamQuestionTypePage(@Valid @RequestBody ExamQuestionTypePageReqVO pageReqVO) {
        return success(examQuestionTypeAdminManager.getExamQuestionTypePage(pageReqVO));
    }

    /**
     * 检查模考题型是否可以删除
     */
    @PostMapping("/check-delete")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:query')")
    public CommonResult<ExamQuestionTypeDeleteCheckRespVO> checkExamQuestionTypeCanDelete(@RequestParam("id") Long id) {
        return success(examQuestionTypeAdminManager.checkExamQuestionTypeCanDelete(id));
    }

    /**
     * 检查模考题型是否存在重复数据
     */
    @PostMapping("/check-duplicate")
    @PreAuthorize("@ss.hasPermission('edu:exam-question-type:query')")
    public CommonResult<ExamQuestionTypeDuplicateCheckRespVO> checkExamQuestionTypeDuplicate(
            @Valid @RequestBody ExamQuestionTypeDuplicateCheckReqVO checkReqVO) {
        return success(examQuestionTypeAdminManager.checkExamQuestionTypeDuplicate(checkReqVO));
    }
}