package com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class QuestionDetailVersionSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionDetailId;

    /**
     * 题号
     */
    @NotNull(message = "题号不能为空")
    private Integer questionNumber;

    /**
     * 题干音频
     */
    private String attachmentAudio;

    /**
     * 题干音频时长
     */
    private Integer attachmentAudioTime;

    /**
     * 题干图片
     */
    private String attachmentImage;

    /**
     * 题干内容
     */
    @NotEmpty(message = "题干内容不能为空")
    private String attachmentContent;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 题目选项 json
     */
    private String options;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 文字题目解析
     */
    private String explainTextCn;

    /**
     * 文字题目解析
     */
    private String explainTextEn;

    /**
     * 文字题目解析
     */
    private String explainTextOt;

    /**
     * 音频题目解析
     */
    private String explainAudio;

    /**
     * 视频题目解析
     */
    private String explainVideo;
    /**
     * 排序序号 也是题号
     */
    private Integer sort;
}