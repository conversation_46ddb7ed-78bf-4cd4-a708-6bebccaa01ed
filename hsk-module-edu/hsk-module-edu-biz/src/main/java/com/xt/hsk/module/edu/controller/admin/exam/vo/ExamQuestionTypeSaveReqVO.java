package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 模考题型 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamQuestionTypeSaveReqVO {

    /**
     * 模考题型ID
     */
    private Long id;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    @InEnum(value = HskEnum.class, message = "HSK等级值必须是 {value} 范围内")
    @NotNull(message = "请选择部分HSK等级")
    private Integer hskLevel;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    @InEnum(value = SubjectEnum.class, message = "科目类型必须是 {value} 范围内")
    @NotNull(message = "请选择科目")
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    @InEnum(value = ExamQuestionTypeUnitEnum.class, message = "部分类型必须是 {value} 范围内")
    @NotNull(message = "请选择部分")
    private Integer unit;

    /**
     * 题型id列表
     */
    @NotEmpty(message = "请选择题型")
    private List<Long> questionTypeIdList;

}