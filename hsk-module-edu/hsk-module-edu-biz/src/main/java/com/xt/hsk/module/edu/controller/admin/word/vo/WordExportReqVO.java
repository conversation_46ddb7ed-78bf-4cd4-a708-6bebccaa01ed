package com.xt.hsk.module.edu.controller.admin.word.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDateTime;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WordExportReqVO extends WordPageReqVO {

}