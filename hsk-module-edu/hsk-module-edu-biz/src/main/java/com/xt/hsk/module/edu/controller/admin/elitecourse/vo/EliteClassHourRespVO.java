package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 精品课-课时 Resp Vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteClassHourRespVO {


    /**
     * 课时ID
     */
    private Long id;

    /**
     * 课程id
     */
    private Long courseId;

    /**
     * 章节id
     */
    private Long chapterId;

    /**
     * 复用id
     */
    private Long reuseId;

    /**
     * 课时名称-中文
     */
    private String classHourNameCn;

    /**
     * 课时名称-英文
     */
    private String classHourNameEn;

    /**
     * 课时名称-其他
     */
    private String classHourNameOt;

    /**
     * 课时类型 1：直播课 2：录播课
     * @see EliteClassHourTypeEnum
     */
    private Integer classHourType;

    /**
     * 视频id
     */
    private Long videoId;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 课时 ID
     */
    private Long classHourId;

    /**
     * 课时时长
     */
    private String duration;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 视频信息列表
     */
    List<EliteClassHourVideoInfoRespVO> videoInfoList;

}