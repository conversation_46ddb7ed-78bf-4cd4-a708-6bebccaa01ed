package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考组卷规则重复检查 请求 VO
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class ExamPaperRuleDuplicateCheckReqVO {

    /**
     * HSK等级
     */
    @InEnum(value = HskEnum.class, message = "HSK等级值必须是 {value} 范围内")
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @InEnum(value = ExamTypeEnum.class, message = "模考类型必须是 {value} 范围内")
    @NotNull(message = "模考类型不能为空")
    private Integer examType;

    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    @InEnum(value = ExamPaperRuleStatusEnum.class, message = "状态值必须是 {value} 范围内")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 排除的ID（用于更新时排除自身，可选）
     */
    private Long excludeId;
} 