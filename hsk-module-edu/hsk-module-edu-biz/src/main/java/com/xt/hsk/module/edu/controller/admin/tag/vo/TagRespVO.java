package com.xt.hsk.module.edu.controller.admin.tag.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class TagRespVO implements VO {


    /**
     * 唯一ID（自增）
     */
    private Long id;


    /**
     * 标签名
     */
    private String tagName;


    /**
     * 备注
     */
    private String remark;


    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 最近更新人
     */
    private String updaterName;
    /**
     * 最近更新人ID
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "updaterName")
    private Long updater;
    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;


}