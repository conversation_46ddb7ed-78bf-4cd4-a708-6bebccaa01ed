package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 模考题型 RESP VO
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamQuestionTypeRespVO implements VO {

    /**
     * 模考题型ID
     */
    private Long id;

    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;

    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = SubjectEnum.class, ref = "subjectDesc")
    private Integer subject;

    /**
     * HSK等级描述
     */
    private String subjectDesc;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamQuestionTypeUnitEnum.class, ref = "unitDesc")
    private Integer unit;

    /**
     * HSK等级描述
     */
    private String unitDesc;

    /**
     * 题型题型id列表
     */
    private String questionTypeIds;

    /**
     * 题型题型名称列表
     */
    private List<String> questionTypeNameList;

    /**
     * 创建者id
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;

}