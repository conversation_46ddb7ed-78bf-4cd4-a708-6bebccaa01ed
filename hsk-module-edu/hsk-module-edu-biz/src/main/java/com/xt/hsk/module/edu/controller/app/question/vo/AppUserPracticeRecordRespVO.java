package com.xt.hsk.module.edu.controller.app.question.vo;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Data
public class AppUserPracticeRecordRespVO {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 教材ID
     */
    private Long textbookId;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 科目
     */
    private Integer subject;

    /**
     * 单元序号
     */
    private Integer unitSort;

    /**
     * 题型ID
     */
    private Long questionTypeId;
    /**
     * 题型名称
     */
    private String questionTypeDesc;

    /**
     * 题目总数量
     */
    private Integer questionNum;

    /**
     * 已作答数量
     */
    private Integer answerNum;

    /**
     * 已正确数量
     */
    private Integer correctNum;

    /**
     * 作答总耗时（秒）
     */
    private Integer answerTime;

    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;

    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;

    /**
     * 记录状态 1 进行中 2 生成报告
     */
    private Integer recordStatus;

    /**
     * 本练习记录是否为最新数据 0 否 1 是
     */
    private Boolean isNewest;
    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;
    /**
     * 本次练习的全部题目明细id(英文逗号拼接)
     */
    private String questionDetailIds;
//    /**
//     * 用户已答题目id(英文逗号拼接)
//     */
//    private String answerQuestionIds = "";
//    /**
//     * 用户答了一半的题目id(英文逗号拼接)
//     */
//    private String halfAnswerQuestionIds = "";
    /**
     * 用户答过的题目明细id(英文逗号拼接)
     */
    private String answerQuestionDetailIds = "";

//    /**
//     * 是否有下个单元
//     */
//    private Boolean hasNextUnit;
//    /**
//     * 下个单元序号
//     */
//    private Integer nextUnitSort;
    /**
     * 是否有下个章节
     */
    private Boolean hasNextChapter;
    /**
     * 下个章节id
     */
    private Long nextChapterId;
    /**
     * 是否有下一个教材
     */
    private Boolean hasNextTextbook;
    /**
     * 下个教材id
     */
    private Long nextTextbookId;
    /**
     * 练习来源
     */
    private String practiceSource;
    /**
     * 用户答题状态详情
     */
    private List<AppUserQuestionStatusVO> userQuestionStatusList;
    /**
     * 用户得分的平均分
     */
    private Integer avgScore;

    /**
     * 互动课科目
     */
    private Integer interactiveCourseSubject;
    /**
     * 互动课单元ID（当练习来源于互动课时）
     */
    private Long interactiveCourseUnitId;
}
