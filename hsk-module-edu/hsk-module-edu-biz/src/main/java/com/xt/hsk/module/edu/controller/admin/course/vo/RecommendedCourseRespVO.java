package com.xt.hsk.module.edu.controller.admin.course.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.ToString;

/**
 * 管理后台 - 推荐课程 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@ToString(callSuper = true)
public class RecommendedCourseRespVO {

    /**
     * 推荐ID
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称-中文
     */
    private String courseNameCn;

    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;

    /**
     * 售卖价格(人民币)
     */
    private BigDecimal sellingPriceCn;

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 上架状态
     */
    private Integer listingStatus;

    /**
     * 是否展示
     */
    private Integer isShow;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建者
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;
    /**
     * 创建者姓名
     */
    private String creatorName;

    /**
     * 更新者
     */
    private String updater;
} 