package com.xt.hsk.module.edu.controller.admin.teacher.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 讲师导出请求参数
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherExportReqVO extends TeacherPageReqVO {

    /**
     * 导出任务名称
     */
    @NotEmpty(message = "任务名称不能为空")
    private String taskName;

} 