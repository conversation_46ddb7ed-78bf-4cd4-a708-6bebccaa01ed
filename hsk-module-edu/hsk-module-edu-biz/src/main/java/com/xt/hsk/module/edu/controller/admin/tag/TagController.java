package com.xt.hsk.module.edu.controller.admin.tag;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.IMPORT_TEMPLATE_SELECT_ERROR;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.IMPORT_TEMPLATE_TYPE_ERROR;
import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.ImportResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagPageReqVO;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagRespVO;
import com.xt.hsk.module.edu.controller.admin.tag.vo.TagSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordTagRespVO;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import com.xt.hsk.module.edu.service.tag.TagManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@Tag(name = "管理后台 - 标签")
@RestController
@RequestMapping("/edu/tag")
@Validated
public class TagController {

    @Resource
    private TagManager tagManager;

    @PostMapping("/create")
    @Operation(summary = "创建标签")
    @PreAuthorize("@ss.hasPermission('edu:tag:create')")
    @LogRecord(type = LogRecordType.TAG_TYPE,
        subType = "创建标签",
        bizNo = "{{#tagId}}",
        success = "创建标签：【{{#tag.tagName}}】")
    public CommonResult<Long> createTag(@Valid @RequestBody TagSaveReqVO createReqVO) {
        return success(tagManager.createTag(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新标签")
    @PreAuthorize("@ss.hasPermission('edu:tag:update')")
    @LogRecord(type = LogRecordType.TAG_TYPE,
        subType = "修改标签",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改标签：【{{#tag.tagName}}】}}")
    public CommonResult<Boolean> updateTag(@Valid @RequestBody TagSaveReqVO updateReqVO) {
        tagManager.updateTag(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除标签")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:tag:delete')")
    @LogRecord(type = LogRecordType.TAG_TYPE,
        subType = "删除标签", bizNo = "{{#id}}",
        success = "删除标签：【{{#tag.tagName}}】")
    public CommonResult<Boolean> deleteTag(@RequestParam("id") Long id) {
        tagManager.deleteTag(id);
        return success(true);
    }

    /**
     * 查看标签引用
     *
     * @param pageReqVO
     */
    @PostMapping("/pageTagRef")
    @Operation(summary = "查看标签引用")
    public CommonResult<PageResult<WordTagRespVO>> getTagRef(@RequestBody TagPageReqVO pageReqVO) {
        PageResult<WordTagRespVO> pageResult = tagManager.getWordTagPage(pageReqVO);
        return success(pageResult);
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @PostMapping("/delete/batch")
    @Operation(summary = "批量删除")
    @LogRecord(type = LogRecordType.TAG_TYPE, subType = "批量删除标签", bizNo = "{{#batchBizNo}}", success = "批量删除标签：共删除{{#deleteCount}}个标签，ID列表：{{#ids}}")
    public CommonResult<Boolean> deleteTagBatch(@RequestBody List<Long> ids) {
        tagManager.deleteTagBatch(ids);
        return success(true);
    }

    /**
     * 获取标签列表
     *
     * @return
     */
    @PostMapping("/get/list")
    @Operation(summary = "获取标签列表")
    public CommonResult<List<TagRespVO>> getTagList() {
        List<TagDO> tagDOList = tagManager.list();
        return success(BeanUtils.toBean(tagDOList, TagRespVO.class));
    }



    @PostMapping("/get")
    @Operation(summary = "获得标签")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:tag:query')")
    public CommonResult<TagRespVO> getTag(@RequestParam("id") Long id) {
        TagDO tag = tagManager.getTag(id);
        return success(BeanUtils.toBean(tag, TagRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得标签分页")
    @PreAuthorize("@ss.hasPermission('edu:tag:query')")
    @TransMethodResult
    public CommonResult<PageResult<TagRespVO>> getTagPage(@Valid @RequestBody TagPageReqVO pageReqVO) {
        PageResult<TagDO> pageResult = tagManager.getTagPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TagRespVO.class));
    }

    /**
     * 下载导入模板
     *
     * @param response 响应
     */
    @PostMapping("/import/template")
    public void downloadImportTemplate(HttpServletResponse response) {
        tagManager.downloadImportTemplate(response);
    }

    /**
     * 导入标签
     */
    @PostMapping("/import")
    public CommonResult<ImportResult> importTag(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return CommonResult.error(IMPORT_TEMPLATE_SELECT_ERROR);
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || (!originalFilename.endsWith(".xlsx") && !originalFilename.endsWith(".xls"))) {
            return CommonResult.error(IMPORT_TEMPLATE_TYPE_ERROR);
        }
        ImportResult result = tagManager.importTag(file);
        return success(result);
    }
}