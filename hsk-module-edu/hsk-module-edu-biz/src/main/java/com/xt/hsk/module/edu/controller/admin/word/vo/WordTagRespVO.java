package com.xt.hsk.module.edu.controller.admin.word.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WordTagRespVO {


    /**
     * ID（自增）
     */
    private Long id;


    /**
     * words的id
     */
    private Long wordId;


    /**
     * 汉字/词语 冗余一部分字词的信息，避开连表
     */
    private String word;


    /**
     * 拼音（如ài）
     */
    private String pinyin;
    /**
     * 标签id）
     */
    private Long tagId;


    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;


}