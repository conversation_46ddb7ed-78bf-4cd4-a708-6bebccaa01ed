package com.xt.hsk.module.edu.controller.admin.exam.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模考题型重复检查 响应 VO
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExamQuestionTypeDuplicateCheckRespVO {

    /**
     * 是否存在重复数据
     */
    private Boolean hasDuplicate;

    /**
     * 重复数据的数量
     */
    private Long duplicateCount;
} 