package com.xt.hsk.module.edu.controller.app.elitecourse.vo;

import java.math.BigDecimal;
import lombok.Data;

/**
 * APP - 精品课 Response VO
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
public class EliteCourseAppRespVO {

    /**
     * 精品课ID
     */
    private Long id;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程封面小图URL
     */
    private String coverUrlSmall;

    /**
     * 课程封面大图URL
     */
    private String coverUrlLarge;

    /**
     * 课时数
     */
    private Integer classHourCount;

    /**
     * 报名人数
     */
    private Integer enrollmentCount;

    /**
     * 划线价格(人民币)
     */
    private BigDecimal originalPrice;
    /**
     * 售卖价格
     */
    private BigDecimal sellingPrice;


} 