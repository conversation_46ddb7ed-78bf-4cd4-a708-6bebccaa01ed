package com.xt.hsk.module.edu.controller.admin.exam;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamStatsReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamStatsRespVO;
import com.xt.hsk.module.edu.manager.exam.admin.ExamDetailAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 模考详情 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Validated
@RestController
@RequestMapping("/edu/exam-detail")
public class ExamDetailAdminController {

    @Resource
    private ExamDetailAdminManager examDetailAdminManager;

    /**
     * 获取模考统计信息
     * 包括参考人数和已考人数
     */
    @PostMapping("/stats")
    @PreAuthorize("@ss.hasPermission('edu:exam:query')")
    public CommonResult<ExamStatsRespVO> getExamStats(@Valid @RequestBody ExamStatsReqVO reqVO) {
        return success(examDetailAdminManager.getExamStats(reqVO));
    }
}