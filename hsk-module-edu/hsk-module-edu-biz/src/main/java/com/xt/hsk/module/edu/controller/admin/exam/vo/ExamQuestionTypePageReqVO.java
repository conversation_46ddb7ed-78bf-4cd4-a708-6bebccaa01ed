package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 模考题型 page req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExamQuestionTypePageReqVO extends PageParam {

    /**
     * HSK等级
     */
    private Integer hskLevel;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;

    /**
     * 题型id列表
     */
    private String questionTypeIds;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 题型id
     */
    private Integer questionTypeId;

    /**
     * 题型id
     */
    private List<Integer> questionTypeIdList;

}