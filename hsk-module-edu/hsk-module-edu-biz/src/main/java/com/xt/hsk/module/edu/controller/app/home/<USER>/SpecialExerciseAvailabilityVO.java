package com.xt.hsk.module.edu.controller.app.home.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 专项练习可用性信息 VO
 *
 * <AUTHOR>
 * @since 2025/07/18
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SpecialExerciseAvailabilityVO {

    /**
     * 各类型专项练习可用性信息列表
     */
    private List<SpecialExerciseTypeAvailabilityVO> typeAvailabilityList;

    /**
     * 专项练习类型可用性信息 VO
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SpecialExerciseTypeAvailabilityVO {

        /**
         * 专项练习类型 1单词连连看 2笔画书写 3连词成句 4卡拉OK
         */
        private Integer type;

        /**
         * 是否有该类型的专项练习题目
         */
        private Boolean hasQuestions;
    }
}
