package com.xt.hsk.module.edu.controller.admin.question.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class QuestionDetailRespVO {


    /**
     * 主键
     */
    private Long id;


    /**
     * 题目ID
     */
    private Long questionId;


    /**
     * 题号
     */
    private Integer questionNumber;


    /**
     * 题干音频
     */
    private String attachmentAudio;


    /**
     * 题干音频时长
     */
    private Integer attachmentAudioTime;


    /**
     * 题干图片
     */
    private String attachmentImage;


    /**
     * 题干内容
     */
    private String attachmentContent;


    /**
     * 参考答案
     */
    private String answer;


    /**
     * 题目选项 json
     */
    private String options;

    private List<OptionContentVO> optionsContent;


    /**
     * 版本号
     */
    private Integer version;


    /**
     * 文字题目解析
     */
    private String explainTextCn;


    /**
     * 文字题目解析
     */
    private String explainTextEn;


    /**
     * 文字题目解析
     */
    private String explainTextOt;


    /**
     * 音频题目解析
     */
    private String explainAudio;


    /**
     * 视频题目解析
     */
    private String explainVideo;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;


    /**
     * 排序序号 也是题号
     */
    private Integer sort;

}