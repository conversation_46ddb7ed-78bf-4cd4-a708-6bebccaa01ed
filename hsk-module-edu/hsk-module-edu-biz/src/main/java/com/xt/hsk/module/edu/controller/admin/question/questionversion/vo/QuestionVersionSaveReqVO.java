package com.xt.hsk.module.edu.controller.admin.question.questionversion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class QuestionVersionSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 题目ID
     */
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /**
     * HSK等级
     */
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 教材ID
     */
    @NotNull(message = "教材ID不能为空")
    private Long textbookId;

    /**
     * 章节ID
     */
    @NotNull(message = "章节ID不能为空")
    private Long chapterId;

    /**
     * 单元ID
     */
    @NotNull(message = "单元ID不能为空")
    private Long unitId;

    /**
     * 题型ID
     */
    @NotNull(message = "题型ID不能为空")
    private Integer typeId;

    /**
     * 科目
     */
    private Integer subject;

    /**
     * 材料音频
     */
    private String materialAudio;

    /**
     * 材料图片
     */
    private String materialImage;

    /**
     * 材料文字
     */
    private String materialContent;

    /**
     * 题目选项 json
     */
    private String options;

    /**
     * 小题数量
     */
    private Integer questionNum;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目编码 #100000开始
     */
    private String questionCode;

}