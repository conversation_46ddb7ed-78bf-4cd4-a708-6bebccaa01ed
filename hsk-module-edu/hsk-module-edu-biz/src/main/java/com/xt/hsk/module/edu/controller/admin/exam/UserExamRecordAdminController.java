package com.xt.hsk.module.edu.controller.admin.exam;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordExportReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordPageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.UserExamRecordRespVO;
import com.xt.hsk.module.edu.dal.dataobject.exam.UserExamRecordDO;
import com.xt.hsk.module.edu.manager.exam.admin.UserExamRecordAdminManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 用户模考记录 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Validated
@RestController
@RequestMapping("/edu/user-exam-record")
@Slf4j
public class UserExamRecordAdminController {

    @Resource
    private UserExamRecordAdminManager userExamRecordAdminManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 根据id获取用户模考记录
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:user-exam-record:query')")
    public CommonResult<UserExamRecordRespVO> getUserExamRecord(@RequestParam("id") Long id) {
        UserExamRecordDO userExamRecord = userExamRecordAdminManager.getUserExamRecord(id);
        return success(BeanUtils.toBean(userExamRecord, UserExamRecordRespVO.class));
    }

    /**
     * 分页获取用户模考记录
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:user-exam-record:query')")
    public CommonResult<PageResult<UserExamRecordPageRespVO>> getUserExamRecordPage(@Valid @RequestBody UserExamRecordPageReqVO pageReqVO) {
        return success(userExamRecordAdminManager.getUserExamRecordPage(pageReqVO));
    }

    /**
     * 导出用户模考记录数据
     */
    @LogRecord(type = LogRecordType.USER_EXAM_RECORD_TYPE, bizNo = "{{#taskId}}", success = "创建用户模考记录异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:user-exam-record:export')")
    public CommonResult<Long> exportUserExamRecord(
            @RequestBody @Valid UserExamRecordExportReqVO exportReqVO) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                    ExportTaskTypeEnum.USER_EXAM_RECORD, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("创建用户模考记录导出任务失败", e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

}