package com.xt.hsk.module.edu.controller.app.home.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户最后学习课程响应VO
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LastLearnedCourseRespVO {

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 课程位置
     */
    private Integer position;

    /**
     * 课程封面
     */
    private String coverUrl;

    /**
     * 有最近学习的课程
     */
    private Boolean hasLastLearnedCourse;

    /**
     * 该hsk等级下有无课程
     */
    private Boolean hasCourse;
}
