package com.xt.hsk.module.edu.controller.admin.interactivecourse.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * 单元词汇信息 vo
 *
 * <AUTHOR>
 * @since 2025/06/30
 */
@Data
public class UnitVocabularyInfoVO implements VO {

    /**
     * 字词ID
     */
    private Long wordId;

    /**
     * 序号
     */
    private Integer sort;

    /**
     * 汉字/词语（如爱）
     */
    private String word;
    /**
     * 汉字/词语 数组
     */
    private List<String> wordList;
    /**
     * 词性
     */
    private String kindsDesc;
    /**
     * 拼音（如ài）
     */
    private String pinyin;
    /**
     * 拼音 数组
     */
    private List<String> pinyinList;

    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;

    /**
     * 音频文件URL
     */
    private String audioUrl;

    /**
     * HSK等级
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;
    /**
     * HSK 级别列表
     */
    private List<Integer> hskLevelList;
    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 含义解释
     */
    private String interpretation;

    /**
     * 收藏状态
     */
    private Boolean collect;

    /**
     * trans 需要
     */
    private Map<String, Object> transMap = new HashMap<>();
}
