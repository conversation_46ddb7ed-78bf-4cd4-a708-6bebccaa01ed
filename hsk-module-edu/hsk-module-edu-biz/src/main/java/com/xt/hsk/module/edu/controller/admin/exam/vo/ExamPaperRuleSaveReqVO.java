package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 模考组卷规则 save req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
public class ExamPaperRuleSaveReqVO {

    /**
     * 组卷规则ID
     */
    private Long id;

    /**
     * 规则名称
     */
    private String name;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    @InEnum(value = HskEnum.class, message = "HSK等级值必须是 {value} 范围内")
    @NotNull(message = "请选择HSK等级")
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @InEnum(value = ExamTypeEnum.class, message = "模考类型必须是 {value} 范围内")
    @NotNull(message = "请选择模考类型")
    private Integer examType;

    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;

    /**
     * 阅读考试时长 (秒)
     */
    @Min(value = 0, message = "阅读考试时长不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "阅读考试时长超出允许的最大值")
    @NotNull(message = "请选择阅读考试时长")
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    @Min(value = 0, message = "书写考试时长不能小于0")
    @Max(value = Integer.MAX_VALUE, message = "书写考试时长超出允许的最大值")
    @NotNull(message = "请选择书写考试时长")
    private Integer writingDuration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    @InEnum(value = ExamPaperRuleStatusEnum.class, message = "模考值必须是 {value} 范围内")
    private Integer status;

    /**
     * 听力单元部分
     */
    private List<ExamPaperRuleQuestionTypeSaveReqVO> listeningSubjectList;

    /**
     * 阅读单元部分
     */
    private List<ExamPaperRuleQuestionTypeSaveReqVO> readingSubjectList;

    /**
     * 书写单元部分
     */
    private List<ExamPaperRuleQuestionTypeSaveReqVO> writingSubjectList;

}