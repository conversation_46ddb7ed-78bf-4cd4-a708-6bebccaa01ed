package com.xt.hsk.module.edu.controller.admin.word;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.service.sourceword.SourceWordManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 汉语词典基础数据")
@RestController
@RequestMapping("/edu/sourceWord")
@Validated
@Slf4j
public class SourceWordController {

    @Resource
    private SourceWordManager sourceWordManager;



    @PostMapping("/executeWord")
    @Operation(summary = "获得汉语词典基础数据分页")
    @PreAuthorize("@ss.hasPermission('edu:word:query')")
    @TransMethodResult()
    public CommonResult<String> executeWord() {
       sourceWordManager.executeMeanField();
        return success("ok");
    }


}