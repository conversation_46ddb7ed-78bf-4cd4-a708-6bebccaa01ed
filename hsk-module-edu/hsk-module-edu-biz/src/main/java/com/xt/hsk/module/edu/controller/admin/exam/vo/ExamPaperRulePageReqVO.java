package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.edu.enums.exam.ExamPaperRuleStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 模考组卷规则 req vo
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExamPaperRulePageReqVO extends PageParam {

    /**
     * 规则名称
     */
    private String name;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    private Integer hskLevel;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    private Integer examType;

    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 状态 1启用 0禁用
     *
     * @see ExamPaperRuleStatusEnum
     */
    private Integer status;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}