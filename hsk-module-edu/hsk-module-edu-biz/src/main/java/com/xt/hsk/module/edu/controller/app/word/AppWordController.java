package com.xt.hsk.module.edu.controller.app.word;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.i18n.LanguageUtils;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordRespVO;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordSearchVo;
import com.xt.hsk.module.edu.controller.app.word.vo.AppWordVo;
import com.xt.hsk.module.edu.controller.app.word.vo.WordSearchResultVo;
import com.xt.hsk.module.edu.service.word.WordManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

@Tag(name = "App - 汉语词典基础数据")
@RestController
@RequestMapping("/edu/word")
@Validated
@Slf4j
public class AppWordController {

    @Resource
    private WordManager wordManager;

    /**
     * 查词翻译
     */
    @PostMapping("/search")
    public CommonResult<PageResult<WordSearchResultVo>> searchPage(@RequestBody AppWordSearchVo appWordSearchVo) {
        PageResult<WordSearchResultVo> pageResult = wordManager.searchPage(appWordSearchVo);
        return CommonResult.success(pageResult);
    }

    /**
     * 获取单词详情byId
     */
    @PostMapping("/getWordById")
    @Operation(summary = "获取单词详情byId")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppWordVo> getWordById(@RequestParam("id") Long id) {
        String language = LanguageUtils.getLanguage();
        AppWordVo word = wordManager.getAppWord(id, language);
        return success(word);
    }

    /**
     * 获取单词列表ByName
     */
    @PostMapping("/getWordByName")
    @Operation(summary = "获取单词列表ByName")
    @Parameter(name = "name", description = "字词", required = true, example = "1024")
    public CommonResult<List<AppWordVo>> getWordByName(@RequestParam("name") String name) {
        List<AppWordVo> words = wordManager.getWordByName(name);
        return success(words);
    }

}
