package com.xt.hsk.module.edu.controller.app.question;

import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.LOCKED;
import static com.xt.hsk.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static com.xt.hsk.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import com.fhs.core.trans.anno.TransMethodResult;
import com.xt.hsk.framework.common.exception.ServiceException;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.app.question.vo.AppQuestionVo;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserAnswerSaveVO;
import com.xt.hsk.module.edu.controller.app.question.vo.AppUserPracticeRecordRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.CallAiCorrectionReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionSearchReqVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionStatisticsRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.QuestionTypeCountRespVO;
import com.xt.hsk.module.edu.controller.app.question.vo.TextbookChapterQuestionCountVO;
import com.xt.hsk.module.edu.service.question.QuestionManager;
import com.xt.hsk.module.edu.service.userquestionanswerrecord.UserQuestionAnswerRecordManager;
import com.xt.hsk.module.thirdparty.dto.coze.CozeRespDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "App - 真题练习")
@RestController
@RequestMapping("/edu/question")
@Validated
@Slf4j
public class AppQuestionController {

    @Resource
    private QuestionManager questionManager;
    @Resource
    private UserQuestionAnswerRecordManager userQuestionAnswerRecordManager;

    /**
     * 获取用户题型列表页
     */
    @SaIgnore
    @Operation(summary = "获取用户题型列表页")
    @PostMapping("/getQuestionStatistics")
    public CommonResult<QuestionStatisticsRespVO> getQuestionStatistics(@RequestBody QuestionSearchReqVO reqVO) {
        long userId = -1L;
        if (StpUtil.isLogin()) {
            userId = StpUtil.getLoginIdAsLong();
        }
        reqVO.setUserId(userId);
        QuestionStatisticsRespVO questionStatisticsRespVO = userQuestionAnswerRecordManager.getQuestionStatistics(reqVO);
        return CommonResult.success(questionStatisticsRespVO);
    }

    /**
     * 获取练习总数
     */
    @SaIgnore
    @Operation(summary = "获取练习总数")
    @PostMapping("/getQuestionPracticeCountList")
    public CommonResult<QuestionTypeCountRespVO> getQuestionPracticeCountList(@Valid @RequestBody QuestionSearchReqVO reqVO) {
        QuestionTypeCountRespVO questionTypeCountRespVO = userQuestionAnswerRecordManager.getQuestionPracticeCountList(reqVO);
        return CommonResult.success(questionTypeCountRespVO);
    }

    /**
     * 获取题目练习列表
     */
    @SaIgnore
    @Operation(summary = "获取题目练习列表")
    @PostMapping("/getQuestionPracticeList")
    public CommonResult<List<TextbookChapterQuestionCountVO>> getQuestionPracticeList(@Valid @RequestBody QuestionSearchReqVO pageReqVO) {
        long userId = -1L;
        if (StpUtil.isLogin()) {
            userId = StpUtil.getLoginIdAsLong();
        }
        pageReqVO.setUserId(userId);
        List<TextbookChapterQuestionCountVO> questionPracticeRespVOList = userQuestionAnswerRecordManager.getQuestionPracticeList(pageReqVO);
        return CommonResult.success(questionPracticeRespVOList);
    }

    //    /**
//     * 查询用户是否有未完成的作答记录
//     */
//    @Operation(summary = "查询用户未完成的作答记录")
//    @GetMapping("/hasUnfinishedPratice")
//    public CommonResult<Long> hasUnfinishedPratice() {
//        if (StpUtil.isLogin()) {
//            return CommonResult.success(userQuestionAnswerRecordManager.hasUnfinishedPratice(StpUtil.getLoginIdAsLong()));
//        }else {
//            return CommonResult.success(-1L);
//        }
//
//
//    }
    @Operation(summary = "开始练习")
    @PostMapping("/startPractice")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<AppUserPracticeRecordRespVO> startPractice(@RequestBody QuestionSearchReqVO reqVO) {
        if (StpUtil.isLogin()) {
            long userId = StpUtil.getLoginIdAsLong();
            reqVO.setUserId(userId);
            return CommonResult.success(userQuestionAnswerRecordManager.startPractice(reqVO));
        } else {
            throw new ServiceException(UNAUTHORIZED);
        }

    }

    /**
     * 获取题目详情
     */
    @SaIgnore
    @Operation(summary = "获取题目详情")
    @PostMapping("/getQuestionDetails")
    @TransMethodResult
    public CommonResult<List<AppQuestionVo>> getQuestionDetails(@RequestBody QuestionSearchReqVO reqVO) {
        return CommonResult.success(userQuestionAnswerRecordManager.getQuestionDetails(reqVO));
    }

    /**
     * 获取学习报告
     */
    @Operation(summary = "获取学习报告")
    @PostMapping("/getStudyReport")
    public CommonResult<AppUserPracticeRecordRespVO> getStudyReport(@RequestBody QuestionSearchReqVO reqVO) {
        return CommonResult.success(userQuestionAnswerRecordManager.getStudyReport(reqVO));
    }

    /**
     * 获取报告中的题目详情
     */
    @Operation(summary = "获取报告中的题目详情")
    @PostMapping("/getReportQuestionDetails")
    @TransMethodResult
    @Deprecated
    public CommonResult<AppQuestionVo> getReportQuestionDetails(@RequestBody QuestionSearchReqVO reqVO) {
        return CommonResult.success(userQuestionAnswerRecordManager.getReportQuestionDetails(reqVO));
    }

    /**
     * 保存用户作答数据
     */
    @Operation(summary = "保存用户作答数据")
    @PostMapping("/saveUserQuestionAnswerRecord")
    public CommonResult<Long> saveUserQuestionAnswerRecord(@RequestBody AppUserAnswerSaveVO reqVO) {
        if (reqVO.getUserAnswerDetailList() == null || reqVO.getUserAnswerDetailList().isEmpty()) {
            throw exception(LOCKED);
        }
        Long questionDetailId = reqVO.getUserAnswerDetailList().get(0).getQuestionDetailId();
        return CommonResult.success(userQuestionAnswerRecordManager.saveUserQuestionAnswerRecord(reqVO, StpUtil.getLoginIdAsLong(), questionDetailId));
    }

    /**
     * 提交用户练习结束
     */
    @Operation(summary = "提交用户练习结束")
    @Parameters({
            @Parameter(name = "practiceId", description = "练习ID", required = true, example = "1"),
            @Parameter(name = "interactiveCourseUnitId", description = "互动课单元ID", required = false, example = "1")
    })
    @PostMapping("/commitUserPracticeEnd")
    public CommonResult<Long> commitUserPracticeEnd(
            @RequestParam("practiceId") Long practiceId,
            @RequestParam(value = "interactiveCourseUnitId", required = false) Long interactiveCourseUnitId) {
        if (StpUtil.isLogin()) {
            return CommonResult.success(userQuestionAnswerRecordManager.commitUserPracticeEnd(practiceId, StpUtil.getLoginIdAsLong(), interactiveCourseUnitId));
        } else {
            throw exception(UNAUTHORIZED);
        }
    }
//
//    /**
//     * ai一键润色
//     */
//    @Operation(summary = "ai一键润色")
//    @PostMapping("/oneClickPolishing")
//    public CommonResult<String> oneClickPolishing(@RequestBody QuestionSearchReqVO reqVO) {
//        return CommonResult.success(userQuestionAnswerRecordManager.oneClickPolishing(reqVO));
//    }

//    /**
//     * 查看用户今天剩余真题ai批改次数
//     */
//    @Operation(summary = "查看用户今天剩余真题ai批改次数")
//    @GetMapping("/getUserQuestionAiCorrectionCount")
//    public CommonResult<Integer> getUserQuestionAiCorrectionCount() {
//        if (StpUtil.isLogin()) {
//            return CommonResult.success(userQuestionAnswerRecordManager.getUserQuestionAiCorrectionCount(StpUtil.getLoginIdAsLong()));
//        } else {
//            return CommonResult.success(0);
//        }
//    }

    /**
     * 调用AI批改
     */
    @Operation(summary = "调用AI批改")
    @PostMapping("/questionCallAiCorrection")
    public CommonResult<Integer> questionCallAiCorrection(@RequestBody CallAiCorrectionReqVO reqVO) {
        long userId = StpUtil.getLoginIdAsLong();
        reqVO.setUserId(userId);
        return CommonResult.success(userQuestionAnswerRecordManager.questionCallAiCorrection(reqVO));
    }

    /**
     * 获取Ai批改结果
     */
    @Operation(summary = "获取Ai批改结果")
    @PostMapping("/getQuestionAiCorrectionResult")
    public CommonResult<CozeRespDto> getQuestionAiCorrectionResult(@RequestBody CallAiCorrectionReqVO reqVO) {
        return CommonResult.success(userQuestionAnswerRecordManager.getQuestionAiCorrectionResult(reqVO));
    }

    /**
     * 获取个人中心练习界面教材章节列表
     */
    @Operation(summary = "获取个人中心练习界面教材章节列表")
    @PostMapping("/getUserPracticeTextbookChapterList")
    public CommonResult<List<TextbookChapterQuestionCountVO>> getUserPracticeTextbookChapterList(@Valid @RequestBody QuestionSearchReqVO pageReqVO) {
        pageReqVO.setUserId(StpUtil.getLoginIdAsLong());
        return CommonResult.success(userQuestionAnswerRecordManager.getUserPracticeTextbookChapterList(pageReqVO));
    }

    /**
     * 获取个人中心练习界面单元题型列表
     */
    @Operation(summary = "获取个人中心练习界面单元题型列表")
    @PostMapping("/getUserPracticeUnitQuestionTypeList")
    public CommonResult<QuestionStatisticsRespVO> getUserPracticeUnitQuestionTypeList(@Valid @RequestBody QuestionSearchReqVO pageReqVO) {
        pageReqVO.setUserId(StpUtil.getLoginIdAsLong());
        return CommonResult.success(userQuestionAnswerRecordManager.getUserPracticeUnitQuestionTypeList(pageReqVO));
    }

}
