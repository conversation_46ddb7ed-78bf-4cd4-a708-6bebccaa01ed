package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * APP 互动课程页面 req vo
 *
 * <AUTHOR>
 * @since 2025/06/09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppInteractiveCoursePageReqVO extends PageParam {

    /**
     * HSK 等级
     */
    @NotNull(message = "请选择hsk等级")
    private Integer hskLevel;
}
