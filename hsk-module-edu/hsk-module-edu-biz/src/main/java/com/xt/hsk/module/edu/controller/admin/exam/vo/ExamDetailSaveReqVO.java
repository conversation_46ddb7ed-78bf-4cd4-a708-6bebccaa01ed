package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.enums.SubjectEnum;
import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考详情 Save req vo
 *
 * <AUTHOR>
 * @since 2025-07-08
 */
@Data
public class ExamDetailSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 模考id
     */
    @NotNull(message = "模考id不能为空")
    private Long examId;

    /**
     * 科目
     *
     * @see SubjectEnum
     */
    private Integer subject;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;

    /**
     * 模考题型ID
     */
    @NotNull(message = "模考题型ID不能为空")
    private Long examQuestionTypeId;

    /**
     * 题型id列表
     */
    @NotEmpty(message = "题型id列表不能为空")
    private String questionTypeIds;

    /**
     * 题型名称列表
     */
    private String questionNames;

    /**
     * 题目数量
     */
    @NotNull(message = "题目数量不能为空")
    private Integer questionCount;

    /**
     * 题目信息
     */
    private String questions;

}