package com.xt.hsk.module.edu.controller.admin.word.vo;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class WordTagSaveReqVO {

    /**
     * ID（自增）
     */
    private Long id;

    /**
     * words的id
     */
    @NotNull(message = "words的id不能为空")
    private Long wordId;

    /**
     * 汉字/词语 冗余一部分字词的信息，避开连表
     */
    @NotEmpty(message = "汉字/词语 冗余一部分字词的信息，避开连表不能为空")
    private String word;

    /**
     * 拼音（如ài）
     */
    private String pinyin;

    /**
     * 标签id）
     */
    @NotNull(message = "标签id）不能为空")
    private Long tagId;

}