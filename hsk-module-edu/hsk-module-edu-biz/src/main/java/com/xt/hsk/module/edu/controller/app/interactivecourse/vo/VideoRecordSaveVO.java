package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.enums.interactivecourse.RecordSaveOptionEnum;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 视频观看 保存 VO
 *
 * <AUTHOR>
 * @since 2025/07/07
 */
@Data
public class VideoRecordSaveVO {
    /**
     * 课程单元id
     */
    @NotNull(message = "unitId不能为空")
    private Long unitId;
    /**
     * 视频记录id
     */
    private Long videoRecordId;
    /**
     * 视频总时间长(单位秒)
     */
    @NotNull(message = "视频总时间不能为空")
    @Min(value = 0, message = "视频总时长不能小于0")
    private Integer videoDuration;
    /**
     * 用户观看总时长(单位秒)
     */
    @NotNull(message = "用户观看时长不能为空")
    @Min(value = 0, message = "用户观看不能小于0")
    private Integer viewingDuration;

    /**
     * 保存选项 1-不保存 2-保存
     * @see RecordSaveOptionEnum
     */
    @NotNull(message = "保存选项不能为空")
    private Integer saveOption;
}
