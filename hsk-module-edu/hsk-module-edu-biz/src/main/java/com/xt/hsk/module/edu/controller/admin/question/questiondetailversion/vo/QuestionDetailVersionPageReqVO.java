package com.xt.hsk.module.edu.controller.admin.question.questiondetailversion.vo;

import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionDetailVersionPageReqVO extends PageParam {

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题目ID
     */
    private Long questionDetailId;

    /**
     * 题号
     */
    private Integer questionNumber;

    /**
     * 题干音频
     */
    private String attachmentAudio;

    /**
     * 题干音频时长
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private Integer[] attachmentAudioTime;

    /**
     * 题干图片
     */
    private String attachmentImage;

    /**
     * 题干内容
     */
    private String attachmentContent;

    /**
     * 参考答案
     */
    private String answer;

    /**
     * 题目选项 json
     */
    private String options;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 文字题目解析
     */
    private String explainTextCn;

    /**
     * 文字题目解析
     */
    private String explainTextEn;

    /**
     * 文字题目解析
     */
    private String explainTextOt;

    /**
     * 音频题目解析
     */
    private String explainAudio;

    /**
     * 视频题目解析
     */
    private String explainVideo;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    /**
     * 排序序号 也是题号
     */
    private Integer sort;
}