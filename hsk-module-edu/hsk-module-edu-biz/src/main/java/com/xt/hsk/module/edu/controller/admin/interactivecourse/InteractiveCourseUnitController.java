package com.xt.hsk.module.edu.controller.admin.interactivecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.idempotent.core.annotation.Idempotent;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitExportReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitPageReqVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitRespVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseUnitSaveReqVO;
import com.xt.hsk.module.edu.manager.interactivecourse.admin.InteractiveCourseUnitManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 互动课单元接口
 *
 * <AUTHOR>
 * @since 2025/01/01
 */
@RestController
@RequestMapping("/edu/interactive-course-unit")
@Validated
@Slf4j
public class InteractiveCourseUnitController {

    @Resource
    private InteractiveCourseUnitManager interactiveCourseUnitManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建互动课单元
     */
    @Idempotent()
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:create')")
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE,
        subType = "创建互动课单元", bizNo = "{{#unitId}}",
        success = "创建互动课单元：【{{#unit.unitName}}】，课程：{{#courseName}}")
    public CommonResult<Long> createInteractiveCourseUnit(
        @Valid @RequestBody InteractiveCourseUnitSaveReqVO reqVO) {
        return success(interactiveCourseUnitManager.createInteractiveCourseUnitNew(reqVO));
    }


    /**
     * 更新互动课单元
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:update')")
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE, subType = "修改互动课单元", bizNo = "{{#updateReqVO.id}}", success = "修改互动课单元：【{{#unit.unitNameCn}}】，课程：{{#courseName}}")
    public CommonResult<Boolean> updateInteractiveCourseUnit(@Valid @RequestBody InteractiveCourseUnitSaveReqVO updateReqVO) {
        interactiveCourseUnitManager.updateInteractiveCourseUnitNew(updateReqVO);
        return success(true);
    }

    /**
     * 删除互动课单元
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE, subType = "删除互动课单元", bizNo = "{{#id}}", success = "删除互动课单元：【{{#unit.unitName}}】，课程：{{#courseName}}")
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:delete')")
    public CommonResult<Boolean> deleteInteractiveCourseUnit(@RequestParam("id") Long id) {
        interactiveCourseUnitManager.deleteInteractiveCourseUnit(id);
        return success(true);
    }


    /**
     * 获得互动课单元（新版本）
     */
    @GetMapping("/get/v2")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:query')")
    public CommonResult<InteractiveCourseUnitRespVO> getInteractiveCourseUnitV2(
        @RequestParam("id") Long id) {
        return success(interactiveCourseUnitManager.getInteractiveCourseUnitDetail(id));
    }

    /**
     * 获得互动课单元分页
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:query')")
    public CommonResult<PageResult<InteractiveCourseUnitRespVO>> getInteractiveCourseUnitPage(
        @RequestBody @Valid InteractiveCourseUnitPageReqVO pageReqVO) {
        return success(interactiveCourseUnitManager.getInteractiveCourseUnitVoPage(pageReqVO));
    }

    /**
     * 更新互动课单元状态
     *
     * @param id 互动课单元id
     */
    @Parameter(name = "id", description = "互动课", required = true)
    @PutMapping("/update-status/{id}")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:update')")
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE, subType = "更新互动课单元状态", bizNo = "{{#id}}", success = "{{#statusText}}互动课单元：【{{#unit.unitName}}】")
    public CommonResult<Boolean> updateInteractiveCourseUnitStatus(@PathVariable("id") Long id) {
        interactiveCourseUnitManager.updateStatus(id);
        return success(true);
    }

    /**
     * 更新互动课单元排序
     */
    @PutMapping("/update-sort")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:update')")
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE, subType = "修改互动课单元排序", bizNo = "{{#id}}", success = "修改互动课单元排序：【{{#unit.unitName}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    public CommonResult<Boolean> updateInteractiveCourseUnitSort(@RequestParam Long id,
        @RequestParam Integer sort) {
        interactiveCourseUnitManager.updateSort(id, sort);
        return success(true);
    }

    /**
     * 根据媒资ID获取视频信息
     */
    @GetMapping("/video-info")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:query')")
    public CommonResult<InteractiveCourseUnitRespVO.VideoInfoVO> getVideoInfoByAssetId
        (@RequestParam("assetId") String assetId) {
        return success(interactiveCourseUnitManager.getVideoInfoByAssetId(assetId));
    }

    /**
     * 导出互动课单元数据
     */
    @LogRecord(type = LogRecordType.INTERACTIVE_COURSE_UNIT_TYPE, subType = "导出互动课单元", bizNo = "{{#taskId}}", success = "导出互动课单元：创建异步导出任务")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:interactive-course-unit:export')")
    public CommonResult<Long> exportInteractiveCourseUnit(
        @RequestBody @Valid InteractiveCourseUnitExportReqVO exportReqVO)
        throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                ExportTaskTypeEnum.INTERACTIVE_COURSE_UNIT, params);
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
    }

}