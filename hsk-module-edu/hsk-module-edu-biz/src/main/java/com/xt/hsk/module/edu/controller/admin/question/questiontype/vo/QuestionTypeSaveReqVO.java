package com.xt.hsk.module.edu.controller.admin.question.questiontype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class QuestionTypeSaveReqVO {

    /**
     * 题型id
     */
    private Long id;

    /**
     * 题型名称
     */
    @NotEmpty(message = "题型名称不能为空")
    private String nameCn;

    /**
     * 题型名称 英文
     */
    private String nameEn;

    /**
     * 题型名称 其他语言
     */
    private String nameOt;

    /**
     * 科目枚举值（1=听力，2=阅读，4=书写）
     */
    @NotNull(message = "科目枚举值（1=听力，2=阅读，4=书写）不能为空")
    private Integer subject;

    /**
     * hsk等级 com.xt.hsk.framework.common.enums.HskEnum
     */
    private Integer hskLevel;

    /**
     * hsk等级s
     */
    private List<Integer> hskLevels;

}