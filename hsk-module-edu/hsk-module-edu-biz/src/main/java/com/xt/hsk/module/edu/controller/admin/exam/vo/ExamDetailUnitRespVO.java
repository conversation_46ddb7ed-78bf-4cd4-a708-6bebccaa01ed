package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.module.edu.enums.exam.ExamQuestionTypeUnitEnum;
import lombok.Data;

import java.util.List;

/**
 * 模考详情单元 req vo
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class ExamDetailUnitRespVO {

    /**
     * 模考详情ID
     */
    private Long examDetailId;

    /**
     * 单元部分
     *
     * @see ExamQuestionTypeUnitEnum
     */
    private Integer unit;

    /**
     * 单元部分描述
     */
    private String unitDesc;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 模考题型ID
     */
    private Long examQuestionTypeId;

    /**
     * 题型id列表
     */
    private String questionTypeIds;

    /**
     * 题型id列表
     */
    private List<Long> questionTypeIdList;

    /**
     * 题目信息
     */
    private List<ExamDetailQuestionReqVO> questionList;

}