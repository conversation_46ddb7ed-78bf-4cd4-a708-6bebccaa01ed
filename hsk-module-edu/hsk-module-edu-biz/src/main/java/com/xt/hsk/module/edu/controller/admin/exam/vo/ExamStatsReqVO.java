package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.xt.hsk.framework.common.validation.InEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考统计 req vo
 *
 * <AUTHOR>
 * @since 2025-07-11
 */
@Data
public class ExamStatsReqVO {

    /**
     * 模考ID
     */
    @NotNull(message = "请选择模考")
    private Long examId;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @InEnum(value = ExamTypeEnum.class, message = "模考类型值必须是 {value} 范围内")
    @NotNull(message = "请选择模考类型")
    private Integer examType;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    @InEnum(value = ExamSubjectSectionsEnum.class, message = "参与的模考科目值必须是 {value} 范围内")
    @NotNull(message = "请选择参与的模考科目")
    private Integer examSections;
} 