package com.xt.hsk.module.edu.controller.admin.question.vo;

import com.xt.hsk.framework.common.pojo.ExportPageParam;
import com.xt.hsk.module.edu.controller.admin.word.vo.WordPageReqVO;
import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionPageReqVO extends ExportPageParam {

    /**
     * HSK等级
     */
    private Integer hskLevel;
    /**
     * 题目id
     */
    private Long questionId;

    /**
     * 题目来源
     */
    private Long textbookType;

    /**
     * 教材ID
     */
    private Long textbookId;

    /**
     * 章节ID
     */
    private Long chapterId;

    /**
     * 单元ID
     */
    private Long unitId;

    /**
     * 题型ID
     */
    private Integer typeId;

    /**
     * 科目
     */
    private Integer subject;

    /**
     * 材料音频
     */
    private String materialAudio;

    /**
     * 材料图片
     */
    private String materialImage;

    /**
     * 材料文字
     */
    private String materialContent;

    /**
     * 题目选项 json
     */
    private String options;

    /**
     * 小题数量
     */
    private Integer questionNum;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 是否展示 1展示 0不展示
     */
    private Integer isShow;

    /**
     * 版本号
     */
    private Integer version;
    /**
     * 题目编码 #100000开始
     */
    private String questionCode;
    /**
     * 正确作答次数
     */
    private Integer correctAnswerCount;
    /**
     * 总作答次数
     */
    private Integer totalAnswerCount;
    /**
     * 父级id
     */
    private Long parentId;
    /**
     * 单元部分
     */
    private String unitSort;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 题型ID列表
     */
    private List<Long> typeIdList;

}