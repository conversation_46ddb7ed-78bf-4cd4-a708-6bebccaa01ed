package com.xt.hsk.module.edu.controller.app.exam.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户模考作答进度 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserExamAnswerProgressSaveReqVO {

    /**
     * 主键
     */
    private Long id;

    /**
     * 题目ID
     */
    @NotNull(message = "题目id不能为空")
    private Long questionId;

    /**
     * 题目详情id
     */
    @NotNull(message = "题目详情id不能为空")
    private Long questionDetailId;

    /**
     * 题目类型
     */
    @NotNull(message = "题目类型不能为空")
    private Integer questionType;

    /**
     * 进度
     */
    private BigDecimal progress;

    /**
     * 模考记录id
     */
    @NotNull(message = "模考记录id不能为空")
    private Long examRecordId;

    /**
     * 播放类型 1题干 2材料音频
     */
    private Integer playType;

    /**
     * 科目
     */
    @NotNull(message = "科目不能为空")
    private Integer subject;

}