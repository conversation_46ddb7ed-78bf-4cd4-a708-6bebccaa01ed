package com.xt.hsk.module.edu.controller.app.word.vo;

import com.xt.hsk.framework.common.enums.WordKindEnum;
import com.xt.hsk.module.edu.dal.dataobject.tag.TagDO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class AppWordVo {
    /**
     * 例句唯一ID（自增）
     */
    private Long id;

    /**
     * 汉字/词语（如爱）
     */
    private String word;


    /**
     * 拼音（如ài）
     */
    private String pinyin;


    /**
     * 组词
     */
    private List<AppWordCardVo> compounds = new ArrayList<>();


    /**
     * 反义词
     */
    private List<AppWordCardVo> antonyms = new ArrayList<>();


    /**
     * 同义词
     */
    private List<AppWordCardVo> synonyms = new ArrayList<>();


    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;


    /**
     * 音频文件URL
     */
    private String audioUrl;

    /**
     * HSK等级
     */
    private List<Integer> hskLevels;
    /**
     * 等级描述
     */
    private String hskLevelsDesc;

    /**
     * 词性
     */
    private List<WordKindEnum> kinds;
    /**
     * 词性详情
     */
    private Map<Integer, List<AppWordMeaningVo>> kindDetails;
    /**
     * 标签
     */
    private List<TagDO> tags;
    /**
     * 是否被收藏
     */
    private Boolean collect;
}
