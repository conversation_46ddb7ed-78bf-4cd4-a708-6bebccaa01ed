package com.xt.hsk.module.edu.controller.admin.tag.vo;

import lombok.*;

import java.util.*;

import io.swagger.v3.oas.annotations.media.Schema;
import com.xt.hsk.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.xt.hsk.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TagPageReqVO extends PageParam {

    /**
     * 标签名
     */
    private String tagName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态 0开启 1关闭
     */
    private Integer status;

    /**
     * 记录创建时间
     */
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * ids
     */
    private List<Long> ids;

}