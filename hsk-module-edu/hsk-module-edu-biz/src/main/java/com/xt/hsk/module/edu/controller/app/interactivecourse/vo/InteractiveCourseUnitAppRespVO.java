package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import java.util.List;
import lombok.Data;

/**
 * 互动课程单元应用程序 Resp VO
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
@Data
public class InteractiveCourseUnitAppRespVO {
    /**
     * 课程单元ID
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 单元名称
     */
    private String unitName;

    /**
     * 单元类型 1-视频 2-专项练习 3-真题练习
     *
     * @see UnitQuestionSourceTypeEnum
     */
    private Integer questionSource;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 推荐学习时长(秒)
     */
    private Integer recommendedDuration;

    /**
     * 学习状态 0-未开始 1-正在进行 2-已完成
     */
    private Integer learningStatus;

    /**
     * 专项练习类型 1-单词连连看 2-笔画书写 3-连词成句 4-卡拉ok
     */
    private Integer specialPracticeType;

    /**
     * 资源版本
     */
    private Integer resourceVersion;

    /**
     * 上次练习记录信息
     */
    private LastRecordInfoVO lastPracticeRecordInfo;

    /**
     * 专项练习ID列表（当questionSource=2时）
     */
    private Long specialPracticeId;

    /**
     * 真题练习ID列表（当questionSource=3时）
     */
    private List<Long> questionIds;
}
