package com.xt.hsk.module.edu.controller.admin.exam.vo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 模考题型重复检查 请求 VO
 *
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class ExamQuestionTypeDuplicateCheckReqVO {

    /**
     * HSK等级
     */
    @NotNull(message = "HSK等级不能为空")
    private Integer hskLevel;

    /**
     * 科目
     */
    @NotNull(message = "科目不能为空")
    private Integer subject;

    /**
     * 单元部分
     */
    @NotNull(message = "单元部分不能为空")
    private Integer unit;

    /**
     * 排除的ID（用于更新时排除自身，可选）
     */
    private Long excludeId;
} 