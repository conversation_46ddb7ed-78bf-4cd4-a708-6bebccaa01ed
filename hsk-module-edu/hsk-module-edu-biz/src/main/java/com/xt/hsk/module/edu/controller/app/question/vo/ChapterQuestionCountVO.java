package com.xt.hsk.module.edu.controller.app.question.vo;

import lombok.Data;

@Data
public class ChapterQuestionCountVO {
    /**
     * 练习记录id
     */
    private Long practiceRecordId;

    /**
     * 章节ID
     */
    private Long chapterId;
    /**
     * 章节名
     */
    private String chapterName;
    /**
     * 题目数量
     */
    private Integer questionCount;
    /**
     * 正确数量
     */
    private Integer correctCount;
    /**
     * 练习数量
     */
    private Integer exerciseCount;
    /**
     * 错误数量
     */
    private Integer errorCount;
    /**
     * 排序
     */
    private Integer sort;
    /**
     * 练习状态 1 进行中 2 已完成 3未开始
     */
    private Integer practiceStatus;
}
