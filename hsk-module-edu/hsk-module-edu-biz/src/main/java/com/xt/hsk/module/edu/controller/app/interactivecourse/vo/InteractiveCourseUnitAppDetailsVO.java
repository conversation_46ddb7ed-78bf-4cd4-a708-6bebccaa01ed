package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CourseVideoLinkVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.content.CoursewareInfoVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.UnitVocabularyInfoVO;
import com.xt.hsk.module.edu.enums.interactivecourse.UnitQuestionSourceTypeEnum;
import java.util.List;
import lombok.Data;

/**
 * 互动课程单元应用程序 Resp VO
 *
 * <AUTHOR>
 * @since 2025/06/12
 */
@Data
public class InteractiveCourseUnitAppDetailsVO {

    /**
     * 课程单元ID
     */
    private Long id;

    /**
     * 课程ID
     */
    private Long courseId;

    /**
     * 单元名称
     */
    private String unitName;

    /**
     * 单元类型 1-视频 2-专项练习 3-真题练习
     *
     * @see UnitQuestionSourceTypeEnum
     */
    private Integer questionSource;

    /**
     * 封面URL
     */
    private String coverUrl;

    /**
     * 推荐学习时长(秒)
     */
    private Integer recommendedDuration;

    /**
     * 学习状态 1.进行中 2.已完成
     */
    private Integer learningStatus;

    /**
     * 专项练习类型 1-单词连连看 2-笔画书写 3-连词成句 4-卡拉ok
     */
    private Integer specialPracticeType;

    /**
     * 真题练习题目类型（待补充）
     */
    private Integer questionType;

    /**
     * 视频类型 1-视频带课件 2-视频带生词
     */
    private Integer videoType;
    /**
     * 视频名称
     */
    private String videoName;

    /**
     * 视频尺寸比例 1-9:16 2-16:9
     */
    private Integer aspectRatio;

    /**
     * 上次练习记录id
     */
    private Long lastPracticeRecordId;

    /**
     * 视频链接列表（当questionSource=3时有值）
     */
    private List<CourseVideoLinkVO> videoLinkList;

    /**
     * 课件信息列表（当videoType=1时有值）
     */
    private List<CoursewareInfoVO> coursewareList;

    /**
     * 生词信息列表（当videoType=2时有值）
     */
    private List<UnitVocabularyInfoVO> vocabularyList;

    /**
     * 上次视频记录信息
     */
    private LastVideoRecordInfoVO lastVideoRecordInfo;

    /**
     * 下一节课程单元ID（当前课程下下一个排序的章节ID）
     */
    private Long nextUnitId;

    /**
     * 是否曾经完播
     */
    private Boolean hasEverCompleted;
}
