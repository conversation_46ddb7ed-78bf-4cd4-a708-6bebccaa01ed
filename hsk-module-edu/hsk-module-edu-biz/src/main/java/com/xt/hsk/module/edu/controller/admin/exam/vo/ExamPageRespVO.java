package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamPublishStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 模考 分页 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
public class ExamPageRespVO implements VO {

    /**
     * 模考ID
     */
    private Long id;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;

    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 模考组卷规则id
     */
    private Long paperRuleId;

    /**
     * 模考名称
     */
    private String name;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamTypeEnum.class, ref = "typeDesc")
    private Integer type;

    /**
     * 模考类型描述
     */
    private String typeDesc;

    /**
     * 模考封面图片URL
     */
    private String coverUrl;

    /**
     * 模考描述
     */
    private String description;

    /**
     * 听力考试时长 (秒)
     */
    private Integer listeningDuration;

    /**
     * 阅读考试时长 (秒)
     */
    private Integer readingDuration;

    /**
     * 书写考试时长 (秒)
     */
    private Integer writingDuration;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 已参加考试人数
     */
    private Integer examCount;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 发布状态：0-未发布 1-已发布 2-已下架
     *
     * @see ExamPublishStatusEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamPublishStatusEnum.class, ref = "publishStatusDesc")
    private Integer publishStatus;

    /**
     * 发布状态描述
     */
    private String publishStatusDesc;

    /**
     * 创建者id
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 最近更新时间
     */
    private LocalDateTime updateTime;


}