package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.enums.interactivecourse.RecordSaveOptionEnum;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 更新记录保存选项请求VO
 *
 * <AUTHOR>
 * @since 2025/07/17
 */
@Data
public class UpdateRecordSaveOptionReqVO {

    /**
     * 单元ID
     */
    @NotNull(message = "单元ID不能为空")
    private Long unitId;

    /**
     * 业务ID（专项练习ID或真题练习ID等）
     */
    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    /**
     * 业务类型 1-视频观看记录 2-专项练习 3-真题练习
     */
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    /**
     * 保存选项 1-不保存 2-保存
     * @see RecordSaveOptionEnum
     */
    @NotNull(message = "保存选项不能为空")
    private Integer saveOption;
}
