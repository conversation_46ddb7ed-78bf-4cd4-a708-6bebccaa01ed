package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import com.xt.hsk.module.edu.enums.interactivecourse.AICorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.InteractiveCourseStatusEnum;
import com.xt.hsk.module.edu.enums.interactivecourse.RecordSaveOptionEnum;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 互动课单元最后一次练习记录 vo
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
@Data
public class LastRecordInfoVO {

    /**
     * 业务类型 1-视频观看记录 2-专项练习 3-真题练习
     */
    private Integer bizType;
    /**
     * 业务ID 对应其他记录表主键 如果是视频记录这里就是null
     */
    private Long bizId;
    /**
     * 状态 1-进行中 2-已完成
     * @see InteractiveCourseStatusEnum
     */
    private Integer status;
    /**
     * AI批改状态 0-没有批改记录 1-有批改记录
     * @see AICorrectionStatusEnum
     */
    private Integer aiCorrectionStatus;
    /**
     * 正确率
     */
    private BigDecimal accuracy;
    /**
     * 资源版本号
     */
    private Integer resourceVersion;


    //==================================专项练习===================================
    /**
     * 专项练习ID
     */
    private Long gameId;

    //==================================真题练习===================================
    /**
     * 题目ID列表，逗号分隔 真题练习时使用。
     */
    private String questionIds;

    /**
     * 记录保存选项 1-不保存 2-保存
     * 如果选择不保存，下次进入时强制继续练习，不提示重新开始
     * @see RecordSaveOptionEnum
     */
    private Integer saveOption;
}
