package com.xt.hsk.module.edu.controller.admin.exam.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.xt.hsk.framework.common.enums.HskEnum;
import com.xt.hsk.module.edu.enums.exam.ExamCorrectionStatusEnum;
import com.xt.hsk.module.edu.enums.exam.ExamSubjectSectionsEnum;
import com.xt.hsk.module.edu.enums.exam.ExamTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户模考记录 分页 resp vo
 *
 * <AUTHOR>
 * @since 2025-07-10
 */
@Data
public class UserExamRecordPageRespVO implements VO {

    /**
     * 用户模考记录id
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 模考id
     */
    private Long examId;

    /**
     * HSK等级
     *
     * @see HskEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = HskEnum.class, ref = "hskLevelDesc")
    private Integer hskLevel;

    /**
     * HSK等级描述
     */
    private String hskLevelDesc;

    /**
     * 模考类型：1-全真模考 2-30分钟模考 3-15分钟模考
     *
     * @see ExamTypeEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamTypeEnum.class, ref = "examTypeDesc")
    private Integer examType;

    /**
     * 模考类型描述
     */
    private String examTypeDesc;

    /**
     * 参与的模考科目 0-完整模考 1-听力 2-阅读 4-书写
     *
     * @see ExamSubjectSectionsEnum
     */
    private Integer examSections;

    /**
     * 总得分
     */
    private Integer actualScore;

    /**
     * 听力得分
     */
    private Integer listeningScore;

    /**
     * 阅读得分
     */
    private Integer readingScore;

    /**
     * 书写得分
     */
    private Integer writingScore;

    /**
     * 作答总耗时（秒）
     */
    private Integer answerTime;

    /**
     * 作答总耗时（秒）
     */
    private String answerTimeStr;

    /**
     * 开始作答时间
     */
    private LocalDateTime startTime;

    /**
     * 结束作答时间
     */
    private LocalDateTime endTime;

    /**
     * 批改状态 1进行中 2待批改 3已批改
     *
     * @see ExamCorrectionStatusEnum
     */
    @Trans(type = TransType.ENUM, key = "code", target = ExamCorrectionStatusEnum.class, ref = "correctionStatusDesc")
    private Integer correctionStatus;

    /**
     * 批改状态描述
     */
    private String correctionStatusDesc;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 手机区号
     */
    private String countryCode;

}