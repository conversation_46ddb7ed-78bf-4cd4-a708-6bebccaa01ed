package com.xt.hsk.module.edu.controller.app.exam;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamReportPageReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamReportPageRespVO;
import com.xt.hsk.module.edu.manager.exam.app.UserExamRecordAppManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户模考记录 app 控制器
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Validated
@RestController
@RequestMapping("/edu/user-exam-record")
@Slf4j
public class UserExamRecordAppController {

    @Resource
    private UserExamRecordAppManager userExamRecordAppManager;


    /**
     * 获取用户模考报告页成绩
     *
     * @param reqVO 请求参数
     * @return 模考报告数据
     */
    @PostMapping("/report")
    public CommonResult<UserExamReportPageRespVO> getExamReport(@RequestBody UserExamReportPageReqVO reqVO) {
        return CommonResult.success(userExamRecordAppManager.getExamReport(reqVO));
    }
}