package com.xt.hsk.module.edu.controller.app.interactivecourse.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * App互动课程进度 VO
 *
 * <AUTHOR>
 * @since 2025/07/14
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppInteractiveCourseProgressVO {

    /**
     * hsk等级
     */
    private Integer hskLevel;

    /**
     * 课程数量
     */
    private Long courseCount;

    /**
     * 已完成课程数量
     */
    private Long completedCourseCount;

}
