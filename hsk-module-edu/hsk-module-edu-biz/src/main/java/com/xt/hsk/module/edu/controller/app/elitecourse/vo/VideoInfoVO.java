package com.xt.hsk.module.edu.controller.app.elitecourse.vo;

import lombok.Data;

import java.util.List;

@Data
public class VideoInfoVO {

    /**
     * 视频名称-中文
     */
    private String videoNameCn;

    /**
     * 视频名称-英文
     */
    private String videoNameEn;

    /**
     * 视频名称-其他
     */
    private String videoNameOt;

    /**
     * 视频类型 1-视频带课件 2-视频带生词
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoTypeEnum
     */
    private Integer videoType;

    /**
     * 视频尺寸比例 1-9:16 2-16:9
     *
     * @see com.xt.hsk.module.edu.enums.interactivecourse.VideoAspectRatioEnum
     */
    private Integer aspectRatio;

    /**
     * 视频时长，单位：秒
     */
    private Long duration;

    /**
     * 视频链接列表
     */
    private List<CourseVideoLinkVO> videoLinkList;
}