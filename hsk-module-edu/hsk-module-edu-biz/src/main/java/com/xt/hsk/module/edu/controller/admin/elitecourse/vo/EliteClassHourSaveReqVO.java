package com.xt.hsk.module.edu.controller.admin.elitecourse.vo;

import com.xt.hsk.module.edu.enums.elitecourse.EliteClassHourTypeEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 精品课-课时 保存 req vo
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
public class EliteClassHourSaveReqVO {

    /**
     * 课时ID
     */
    private Long id;

    /**
     * 课程id
     */
    @NotNull(message = "课程id不能为空")
    private Long courseId;

    /**
     * 章节id
     */
    @NotNull(message = "请选择章节")
    private Long chapterId;

    /**
     * 复用id
     */
    private Long reuseId;

    /**
     * 课时名称-中文
     */
    @NotEmpty(message = "请输入课时名称")
    private String classHourNameCn;

    /**
     * 课时名称-英文
     */
    private String classHourNameEn;

    /**
     * 课时名称-其他
     */
    private String classHourNameOt;

    /**
     * 课时类型 1：直播课 2：录播课
     * @see EliteClassHourTypeEnum
     */
    private Integer classHourType;

    /**
     * 视频url
     */
    private String videoUrl;

    /**
     * 排序序号
     */
    private Integer sort;

    /**
     * 媒资ID
     */
    private String assetId;

    /**
     * 课时ID列表
     */
    private List<Long> classHourIds;

}