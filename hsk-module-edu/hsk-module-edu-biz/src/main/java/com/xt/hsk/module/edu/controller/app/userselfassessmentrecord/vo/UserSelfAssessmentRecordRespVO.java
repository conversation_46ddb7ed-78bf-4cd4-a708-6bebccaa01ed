package com.xt.hsk.module.edu.controller.app.userselfassessmentrecord.vo;

import com.xt.hsk.module.edu.controller.app.question.vo.AppQuestionVo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class UserSelfAssessmentRecordRespVO {


    /**
     * 主键
     */
    private Long id;

    /**
     * 本次练习的全部题目id(英文逗号拼接)
     */
    private String questionIds;
//    /**
//     * 本次练习的全部题目明细id(英文逗号拼接)
//     */
//    private String questionDetailIds;
//    /**
//     * 本次练习的全部题目
//     */
//    private List<AppQuestionVo> questions;


}