package com.xt.hsk.module.edu.controller.admin.question.questiontype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import cn.idev.excel.annotation.*;

@Data
public class QuestionTypeRespVO {


    /**
     * 题型id
     */
    private Long id;


    /**
     * 题型名称
     */
    private String nameCn;


    /**
     * 题型名称 英文
     */
    private String nameEn;


    /**
     * 题型名称 其他语言
     */
    private String nameOt;


    /**
     * 科目枚举值（1=听力，2=阅读，4=书写）
     */
    private Integer subject;


    /**
     * hsk等级 com.xt.hsk.framework.common.enums.HskEnum
     */
    private Integer hskLevel;
    /**
     * hsk等级
     */
    private List<Integer> hskLevels;

    /**
     * 真题数量
     */
    private Integer questionNumber;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}