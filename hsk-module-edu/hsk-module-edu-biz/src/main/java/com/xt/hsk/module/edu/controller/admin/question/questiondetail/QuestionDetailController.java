package com.xt.hsk.module.edu.controller.admin.question.questiondetail;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailPageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailRespVO;
import com.xt.hsk.module.edu.controller.admin.question.vo.QuestionDetailSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.question.questiondetail.QuestionDetailDO;
import com.xt.hsk.module.edu.service.question.questiondetail.QuestionDetailManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 题目详情")
@RestController
@RequestMapping("/edu/question-detail")
@Validated
public class QuestionDetailController {

    @Resource
    private QuestionDetailManager questionDetailManager;

    @PostMapping("/create")
    @Operation(summary = "创建题目详情")
    @PreAuthorize("@ss.hasPermission('edu:question-detail:create')")
    public CommonResult<Long> createQuestionDetail(@Valid @RequestBody QuestionDetailSaveReqVO createReqVO) {
        return success(questionDetailManager.createQuestionDetail(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新题目详情")
    @PreAuthorize("@ss.hasPermission('edu:question-detail:update')")
    public CommonResult<Boolean> updateQuestionDetail(@Valid @RequestBody QuestionDetailSaveReqVO updateReqVO) {
        questionDetailManager.updateQuestionDetail(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题目详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-detail:delete')")
    public CommonResult<Boolean> deleteQuestionDetail(@RequestParam("id") Long id) {
        questionDetailManager.deleteQuestionDetail(id);
        return success(true);
    }




    @PostMapping("/page")
    @Operation(summary = "获得题目详情分页")
    @PreAuthorize("@ss.hasPermission('edu:question-detail:query')")
    public CommonResult
            <PageResult
                    <QuestionDetailRespVO>> getQuestionDetailPage(@Valid
                                                                  @RequestBody QuestionDetailPageReqVO pageReqVO) {
        PageResult<QuestionDetailDO> pageResult = questionDetailManager.getQuestionDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionDetailRespVO.class));
    }


}