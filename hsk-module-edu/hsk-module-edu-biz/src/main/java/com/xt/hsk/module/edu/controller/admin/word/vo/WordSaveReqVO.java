package com.xt.hsk.module.edu.controller.admin.word.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.*;

import jakarta.validation.constraints.*;

@Data
public class WordSaveReqVO {

    /**
     * id
     */
    private Long id;

    /**
     * 汉字/词语（如爱）
     */
    @NotEmpty(message = "汉字/词语（如爱）不能为空")
    private String word;

    /**
     * 拼音（如ài）
     */
    private String pinyin;

    /**
     * 音标（如ai）
     */
    private String phonetic;

    /**
     * hsk等级
     */
    private List<Integer> hskLevels;

    /**
     * 标签
     */
    private List<Long> tags;

    /**
     * 复合词（多个词语用分号分隔，如爱不忍释; 爱不释手）
     */
    private String compound;

    /**
     * 反义词数组（如[恨, 恶, 憎]）
     */
    private String antonyms;

    /**
     * 同义词数组（如[热爱, 喜爱]）
     */
    private String synonyms;

    /**
     * 是否汉越词 0 否 1 是
     */
    private Integer isSpecial;

    /**
     * 音频文件URL
     */
    private String audioUrl;
    /**
     * 含义
     */
    private List<WordMeaningSaveReqVO> meanings;

}