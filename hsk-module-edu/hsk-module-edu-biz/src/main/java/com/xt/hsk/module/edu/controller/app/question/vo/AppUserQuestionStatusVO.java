package com.xt.hsk.module.edu.controller.app.question.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class AppUserQuestionStatusVO implements Serializable {
    /**
     * id
     */
    private Long questionId;
    /**
     * 题目明细id
     */
    private Long questionDetailId;
    /**
     * 记录id
     */
    private Long recordId;
    /**
     * 记录状态 1 进行中 2 已提交 3 ai批改未完成  4 ai批改完成 5 ai批改失败
     */
    private Integer recordStatus;
    /**
     * 是否正确 0-错误 1-正确
     */
    private Boolean isCorrect;
    /**
     * ai 批改状态 0-未批改 1-已批改
     */
    private Integer aiCorrectStatus;
    /**
     * 序号
     */
    private Integer sort;
    /**
     * 版本
     */
    private Integer version;
}
