package com.xt.hsk.module.edu.controller.app.exam;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamAnswerProgressReqVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamAnswerProgressRespVO;
import com.xt.hsk.module.edu.controller.app.exam.vo.UserExamAnswerProgressSaveReqVO;
import com.xt.hsk.module.edu.manager.exam.app.UserExamAnswerProgressAppManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

/**
 * 用户模考作答进度 app 控制器
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Validated
@RestController
@RequestMapping("/edu/user-exam/answer-progress")
public class UserExamAnswerProgressAppController {

    @Resource
    private UserExamAnswerProgressAppManager userExamAnswerProgressAppManager;


    /**
     * 保存用户模考作答进度
     */
    @PostMapping("/v1/save")
    public CommonResult<UserExamAnswerProgressRespVO> save(@Valid @RequestBody UserExamAnswerProgressSaveReqVO reqVO) {
        return success(userExamAnswerProgressAppManager.save(reqVO));
    }

    /**
     * 获取用户模考作答进度
     */
    @PostMapping("/v1/get")
    public CommonResult<UserExamAnswerProgressRespVO> get(@RequestBody UserExamAnswerProgressReqVO reqVO) {
        return success(userExamAnswerProgressAppManager.get(reqVO));
    }
}