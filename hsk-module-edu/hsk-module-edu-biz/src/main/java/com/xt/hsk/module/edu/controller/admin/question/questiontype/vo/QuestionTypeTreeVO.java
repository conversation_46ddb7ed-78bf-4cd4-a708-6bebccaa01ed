package com.xt.hsk.module.edu.controller.admin.question.questiontype.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 管理后台 - 题型树形结构 VO
 *
 * <AUTHOR>
 * @since 2025/07/11
 */
@Data
public class QuestionTypeTreeVO {

    /**
     * 科目编码
     */
    private Integer subjectCode;

    /**
     * 科目名称
     */
    private String subjectName;

    /**
     * 题型列表
     */
    private List<QuestionTypeItem> types;

    /**
     * 题型项
     */
    @Data
    public static class QuestionTypeItem {
        
        @Schema(description = "题型编号")
        private Long id;
        
        @Schema(description = "题型名称")
        private String name;
    }
} 