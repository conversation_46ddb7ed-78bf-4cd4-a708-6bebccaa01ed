package com.xt.hsk.module.edu.controller.admin.exam;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleBaseInfoRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDuplicateCheckReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleDuplicateCheckRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageReqVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRulePageRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleRespVO;
import com.xt.hsk.module.edu.controller.admin.exam.vo.ExamPaperRuleSaveReqVO;
import com.xt.hsk.module.edu.manager.exam.admin.ExamPaperRuleAdminManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 模考组卷规则 后台 控制器
 *
 * <AUTHOR>
 * @since 2025-07-05
 */
@Validated
@RestController
@RequestMapping("/edu/exam-paper-rule")
public class ExamPaperRuleAdminController {

    @Resource
    private ExamPaperRuleAdminManager examPaperRuleAdminManager;

    /**
     * 创建模考组卷规则
     */
    @PostMapping("/create")
    @LogRecord(type = LogRecordType.EXAM_PAPER_RULE,
        subType = "创建组卷规则", bizNo = "{{#ruleId}}",
        success = "创建组卷规则：【{{#rule.name}}】")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:create')")
    public CommonResult<Long> createExamPaperRule(@Valid @RequestBody ExamPaperRuleSaveReqVO createReqVO) {
        return success(examPaperRuleAdminManager.createExamPaperRule(createReqVO));
    }

    /**
     * 更新模考组卷规则
     */
    @PutMapping("/update")
    @LogRecord(type = LogRecordType.EXAM_PAPER_RULE,
        subType = "修改组卷规则",
        bizNo = "{{#updateReqVO.id}}",
        success = "修改组卷规则：【{{#rule.name}}】")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:update')")
    public CommonResult<Boolean> updateExamPaperRule(@Valid @RequestBody ExamPaperRuleSaveReqVO updateReqVO) {
        examPaperRuleAdminManager.updateExamPaperRule(updateReqVO);
        return success(true);
    }

    /**
     * 删除模考组卷规则
     */
    @DeleteMapping("/delete")
    @LogRecord(type = LogRecordType.EXAM_PAPER_RULE,
        subType = "删除组卷规则", bizNo = "{{#id}}",
        success = "删除组卷规则：【{{#rule.name}}】")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:delete')")
    public CommonResult<Boolean> deleteExamPaperRule(@RequestParam("id") Long id) {
        examPaperRuleAdminManager.deleteExamPaperRule(id);
        return success(true);
    }

    /**
     * 根据id获取模考组卷规则
     */
    @PostMapping("/get")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:query')")
    public CommonResult<ExamPaperRuleRespVO> getExamPaperRule(@RequestParam("id") Long id) {
        return success(examPaperRuleAdminManager.getExamPaperRule(id));
    }

    /**
     * 分页获取模考组卷规则
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:query')")
    public CommonResult<PageResult<ExamPaperRulePageRespVO>> getExamPaperRulePage(@Valid @RequestBody ExamPaperRulePageReqVO pageReqVO) {
        return success(examPaperRuleAdminManager.getExamPaperRulePage(pageReqVO));
    }

    /**
     * 隐藏或者显示
     */
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:update')")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Long id) {
        examPaperRuleAdminManager.updateStatus(id);
        return success(true);
    }

    /**
     * 检查模考组卷规则是否存在重复数据
     */
    @PostMapping("/check-duplicate")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:query')")
    public CommonResult<ExamPaperRuleDuplicateCheckRespVO> checkExamPaperRuleDuplicate(
            @Valid @RequestBody ExamPaperRuleDuplicateCheckReqVO checkReqVO) {
        return success(examPaperRuleAdminManager.checkExamPaperRuleDuplicate(checkReqVO));
    }

    /**
     * 根据HSK等级查询模考组卷规则列表
     */
    @GetMapping("/list-by-hsk")
    @PreAuthorize("@ss.hasPermission('edu:exam-paper-rule:query')")
    public CommonResult<List<ExamPaperRuleBaseInfoRespVO>> getBaseInfoListByHskLevel(
            @RequestParam("hskLevel") Integer hskLevel) {
        return success(examPaperRuleAdminManager.getBaseInfoListByHskLevel(hskLevel));
    }

}