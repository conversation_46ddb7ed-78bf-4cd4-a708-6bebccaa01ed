package com.xt.hsk.module.edu.controller.app.home;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.app.home.vo.HomeInfoReqVO;
import com.xt.hsk.module.edu.controller.app.home.vo.HomeInfoVO;
import com.xt.hsk.module.edu.manager.home.HomeManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * Home 相关接口
 *
 * <AUTHOR>
 * @since 2025/07/15
 */
@Slf4j
@RestController
@RequestMapping("/home")
public class HomeController {

    @Resource
    private HomeManager homeManager;

    /**
     * 首页相关信息
     * @param reqVO 请求参数，包含HSK等级
     * @return 1.最后一次学习的互动课信息 2.专项练习信息
     */
    @SaIgnore
    @PostMapping("/home-info")
    public CommonResult<HomeInfoVO> getHomeInfo(@Valid @RequestBody HomeInfoReqVO reqVO) {
        return CommonResult.success(homeManager.getHomeInfo(reqVO.getHskLevel()));
    }

}
