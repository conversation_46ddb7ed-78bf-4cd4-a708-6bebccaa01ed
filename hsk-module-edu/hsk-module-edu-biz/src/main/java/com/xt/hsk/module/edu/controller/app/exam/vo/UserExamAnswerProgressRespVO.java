package com.xt.hsk.module.edu.controller.app.exam.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户模考作答进度 resp vo
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
@Data
public class UserExamAnswerProgressRespVO {


    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题目详情id
     */
    private Long questionDetailId;

    /**
     * 题目类型
     */
    private Integer questionType;

    /**
     * 进度
     */
    private BigDecimal progress;

    /**
     * 模考记录id
     */
    private Long examRecordId;

    /**
     * 播放类型 1题干 2材料音频
     */
    private Integer playType;

    /**
     * 科目
     */
    private Integer subject;


}