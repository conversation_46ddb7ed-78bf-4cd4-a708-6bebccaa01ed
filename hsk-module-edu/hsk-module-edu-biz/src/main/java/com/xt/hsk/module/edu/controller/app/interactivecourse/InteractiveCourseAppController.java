package com.xt.hsk.module.edu.controller.app.interactivecourse;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import cn.dev33.satoken.annotation.SaIgnore;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseAppDetailVO;
import com.xt.hsk.module.edu.controller.admin.interactivecourse.vo.InteractiveCourseAppRespVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCoursePageReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseProgressVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveCourseReqVO;
import com.xt.hsk.module.edu.controller.app.interactivecourse.vo.AppInteractiveProgressVO;
import com.xt.hsk.module.edu.manager.interactivecourse.app.AppInteractiveCourseManager;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * APP - 互动课接口
 *
 * <AUTHOR>
 * @since 2025/05/23
 */
@RestController
@RequestMapping("/edu/interactive-course")
@Validated
@Slf4j
public class InteractiveCourseAppController {

    @Resource
    private AppInteractiveCourseManager appInteractiveCourseManager;


    /**
     * 互动课列表
     */
    @SaIgnore
    @PostMapping("/v1/page")
    public CommonResult<PageResult<InteractiveCourseAppRespVO>> getInteractiveCoursePage(
        @RequestBody @Valid AppInteractiveCoursePageReqVO reqVO) {
        return success(appInteractiveCourseManager.getInteractiveCourseVoPage(reqVO));
    }

    /**
     * 获取互动课详情
     */
    @SaIgnore
    @PostMapping("/v1/get")
    public CommonResult<InteractiveCourseAppDetailVO> getInteractiveCourseDetail(
        @RequestBody @Valid AppInteractiveCourseReqVO reqVO) {
        return success(appInteractiveCourseManager.getInteractiveCourseDetail(reqVO));
    }

    /**
     * 获得互动课学习进度
     */
    @SaIgnore
    @PostMapping("/v1/get-progress")
    public CommonResult<AppInteractiveCourseProgressVO> getInteractiveCourseProgress(
        @RequestBody @Valid AppInteractiveProgressVO reqVO) {
        return success(appInteractiveCourseManager.getInteractiveCourseProgress(reqVO));
    }

}
