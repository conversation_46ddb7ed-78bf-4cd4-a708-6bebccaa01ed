package com.xt.hsk.module.edu.controller.admin.teacher;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.framework.common.util.object.BeanUtils;
import com.xt.hsk.framework.idempotent.core.annotation.Idempotent;
import com.xt.hsk.module.edu.controller.admin.elitecourse.vo.EliteCourseExportReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherExportReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherPageReqVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherRespVO;
import com.xt.hsk.module.edu.controller.admin.teacher.vo.TeacherSaveReqVO;
import com.xt.hsk.module.edu.dal.dataobject.teacher.TeacherDO;
import com.xt.hsk.module.edu.manager.teacher.TeacherManager;
import com.xt.hsk.module.infra.api.export.ExportTaskApi;
import com.xt.hsk.module.infra.api.export.ExportTaskParams;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 讲师管理
 *
 * <AUTHOR>
 * @since 2025/06/03
 */
@RestController
@RequestMapping("/edu/teacher")
@Validated
@Slf4j
public class TeacherController {

    @Resource
    private TeacherManager teacherManager;

    @Resource
    private ExportTaskApi exportTaskApi;

    /**
     * 创建讲师
     */
    @Idempotent()
    @PostMapping("/create")
    @PreAuthorize("@ss.hasPermission('edu:teacher:create')")
    @LogRecord(type = LogRecordType.TEACHER_TYPE,
        subType = "创建讲师", bizNo = "{{#teacherId}}",
        success = "创建讲师：【{{#teacher.teacherNameCn}}】，手机号：{{#teacher.mobile}}")
    public CommonResult<Long> createTeacher(@Valid @RequestBody TeacherSaveReqVO createReqVO) {
        return success(teacherManager.createTeacher(createReqVO));
    }

    /**
     * 更新讲师
     */
    @PutMapping("/update")
    @PreAuthorize("@ss.hasPermission('edu:teacher:update')")
    @LogRecord(type = LogRecordType.TEACHER_TYPE,
        subType = "修改讲师", bizNo = "{{#updateReqVO.id}}",
        success = "修改讲师：【{{#teacher.teacherNameCn}}】，手机号：{{#teacher.mobile}}")
    public CommonResult<Boolean> updateTeacher(@Valid @RequestBody TeacherSaveReqVO updateReqVO) {
        teacherManager.updateTeacher(updateReqVO);
        return success(true);
    }

    /**
     * 删除讲师
     */
    @Parameter(name = "id", description = "讲师ID", required = true)
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('edu:teacher:delete')")
    @LogRecord(type = LogRecordType.TEACHER_TYPE,
        subType = "删除讲师", bizNo = "{{#id}}",
        success = "删除讲师：【{{#teacher.teacherNameCn}}】，手机号：{{#teacher.mobile}}")
    public CommonResult<Boolean> deleteTeacher(@RequestParam("id") Long id) {
        teacherManager.deleteTeacher(id);
        return success(true);
    }

    /**
     * 获得讲师
     */
    @Parameter(name = "id", description = "讲师ID", required = true)
    @GetMapping("/get")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:teacher:query')")
    public CommonResult<TeacherRespVO> getTeacher(@RequestParam("id") Long id) {
        TeacherDO teacher = teacherManager.getTeacher(id);
        return success(BeanUtils.toBean(teacher, TeacherRespVO.class));
    }

    /**
     * 获得讲师分页
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('edu:teacher:query')")
    public CommonResult<PageResult<TeacherRespVO>> getTeacherPage(
        @RequestBody @Valid TeacherPageReqVO pageReqVO) {
        return success(teacherManager.getTeacherVoPage(pageReqVO));
    }

    /**
     * 隐藏或者显示
     */
    @Parameter(name = "id", description = "讲师ID", required = true)
    @PutMapping("/update-status")
    @PreAuthorize("@ss.hasPermission('edu:teacher:update')")
    @LogRecord(type = LogRecordType.TEACHER_TYPE, subType = "更新讲师状态", bizNo = "{{#id}}", success = "{{#statusText}}讲师：【{{#teacher.teacherNameCn}}】")
    public CommonResult<Boolean> updateStatus(@RequestParam("id") Long id) {
        teacherManager.updateStatus(id);
        return success(true);
    }

    /**
     * 修改排序
     */
    @Parameters({
        @Parameter(name = "id", description = "讲师ID", required = true),
        @Parameter(name = "sort", description = "排序序号", required = true)
    })
    @PutMapping("/update-sort")
    @PreAuthorize("@ss.hasPermission('edu:teacher:update')")
    @LogRecord(type = LogRecordType.TEACHER_TYPE, subType = "修改讲师排序", bizNo = "{{#id}}", success = "修改讲师排序：【{{#teacher.teacherNameCn}}】从第{{#oldSort}}位调整到第{{#sort}}位")
    public CommonResult<Boolean> updateSort(@RequestParam("id") Long id,
        @RequestParam("sort") Integer sort) {
        teacherManager.updateSort(id, sort);
        return success(true);
    }

    /**
     * 导出讲师数据
     */
    @LogRecord(type = LogRecordType.TEACHER_TYPE, bizNo = "{{#taskId}}", success = "创建讲师异步导出任务,任务名称【{{taskName}}】")
    @PostMapping("/export")
    @PreAuthorize("@ss.hasPermission('edu:teacher:export')")
    public CommonResult<Long> exportTeacher(@RequestBody @Valid TeacherExportReqVO exportReqVO) {
        log.info("=== 开始创建讲师导出任务 === 请求参数: {}", exportReqVO);

        try {
            log.info("步骤1: 序列化导出参数");

            ObjectMapper objectMapper = new ObjectMapper();
            // 将导出请求参数和任务名称一起传递
            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);

            log.info("步骤1.1: 参数序列化完成, params: {}", params);

            log.info("步骤2: 调用导出任务API创建任务");

            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                ExportTaskTypeEnum.TEACHER, params);

            log.info("=== 讲师导出任务创建成功 === taskId: {}, taskName: {}", taskId,
                exportReqVO.getTaskName());

            // 记录请求日志上下文
            LogRecordContext.putVariable("taskName", exportReqVO.getTaskName());
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("=== 创建讲师导出任务失败 === 错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

    /**
     * 导出讲师课程数据
     */
    @LogRecord(type = LogRecordType.TEACHER_TYPE, bizNo = "{{#taskId}}", success = "创建讲师课程导出任务,任务名称【{{taskName}}】")
    @PostMapping("/export-course")
    @PreAuthorize("@ss.hasPermission('edu:teacher:export')")
    public CommonResult<Long> exportTeacherCourse(
        @RequestBody @Valid EliteCourseExportReqVO exportReqVO) {
        log.info("=== 开始创建讲师课程导出任务 ===请求参数: {}", exportReqVO);

        try {
            log.info("步骤1: 序列化导出参数");
            ObjectMapper objectMapper = new ObjectMapper();

            ExportTaskParams taskParams = new ExportTaskParams();
            taskParams.setTaskName(exportReqVO.getTaskName());
            taskParams.setQueryParams(exportReqVO);

            String params = objectMapper.writeValueAsString(taskParams);
            log.info("步骤1.1: 参数序列化完成, params: {}", params);

            log.info("步骤2: 调用导出任务API创建任务");
            Long taskId = exportTaskApi.createExportTask(exportReqVO.getTaskName(),
                ExportTaskTypeEnum.TEACHER_ELITE_COURSE, params);

            log.info("=== 讲师课程导出任务创建成功 === taskId: {}, taskName: {}", taskId,
                exportReqVO.getTaskName());

            // 记录请求日志上下文
            LogRecordContext.putVariable("taskName", exportReqVO.getTaskName());
            LogRecordContext.putVariable("taskId", taskId);
            return success(taskId);
        } catch (Exception e) {
            log.error("=== 创建讲师课程导出任务失败 === 错误: {}", e.getMessage(), e);
            return CommonResult.error(500, "创建导出任务失败：" + e.getMessage());
        }
    }

} 