package com.xt.hsk.module.edu.controller.admin.question.questiontype;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypePageReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeRespVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeSaveReqVO;
import com.xt.hsk.module.edu.controller.admin.question.questiontype.vo.QuestionTypeTreeVO;
import com.xt.hsk.module.edu.service.question.questiontype.QuestionTypeManager;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 题型管理")
@RestController
@RequestMapping("/edu/question-type")
@Validated
public class QuestionTypeController {

    @Resource
    private QuestionTypeManager questionTypeManager;

    @PostMapping("/create")
    @Operation(summary = "创建题型")
    @PreAuthorize("@ss.hasPermission('edu:question-type:create')")
    public CommonResult<Long> createQuestionType(@Valid @RequestBody QuestionTypeSaveReqVO createReqVO) {
        return success(questionTypeManager.createQuestionType(createReqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新题型")
    @PreAuthorize("@ss.hasPermission('edu:question-type:update')")
    public CommonResult<Boolean> updateQuestionType(@Valid @RequestBody QuestionTypeSaveReqVO updateReqVO) {
        questionTypeManager.updateQuestionType(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除题型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('edu:question-type:delete')")
    public CommonResult<Boolean> deleteQuestionType(@RequestParam("id") Long id) {
        questionTypeManager.deleteQuestionType(id);
        return success(true);
    }

    @PostMapping("/get")
    @Operation(summary = "获得题型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('edu:question-type:query')")
    public CommonResult<QuestionTypeRespVO> getQuestionType(@RequestParam("id") Long id) {
        QuestionTypeRespVO questionType = questionTypeManager.getQuestionType(id);
        return success(questionType);
    }

    @PostMapping("/list")
    @Operation(summary = "获得题型")
    @PreAuthorize("@ss.hasPermission('edu:question-type:query')")
    public CommonResult<List<QuestionTypeRespVO>> getQuestionTypePage(@Valid
                                                                          @RequestBody QuestionTypePageReqVO pageReqVO) {
        List<QuestionTypeRespVO> pageResult = questionTypeManager.getQuestionTypeList(pageReqVO);
        return success(pageResult);
    }

    /**
     * 获取所有题型
     */
    @PostMapping("/getAll")
    @Operation(summary = "获取所有题型")
    public CommonResult<List<QuestionTypeRespVO>> getAllQuestionType() {
        List<QuestionTypeRespVO> questionTypes = questionTypeManager.getAllQuestionType();
        return success(questionTypes);
    }

    /**
     * 获取题型树形结构，按科目分组
     */
    @PostMapping("/tree")
    @Operation(summary = "获取题型树形结构，按科目分组")
    @PreAuthorize("@ss.hasPermission('edu:question-type:query')")
    public CommonResult<List<QuestionTypeTreeVO>> getQuestionTypeTree() {
        List<QuestionTypeTreeVO> tree = questionTypeManager.getQuestionTypeTree();
        return success(tree);
    }

}