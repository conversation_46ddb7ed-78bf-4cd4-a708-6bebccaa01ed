<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.edu.dal.mysql.exam.ExamMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="getAppExamPage" resultType="com.xt.hsk.module.edu.controller.app.exam.vo.ExamAppPageRespVO">
        SELECT e.id,
               e.id AS exam_id,
               e.hsk_level,
               e.`name`,
               e.type,
               e.cover_url,
               r.id AS exam_record_id,
               r.correction_status,
               r.total_score,
               r.actual_score,
               r.practice_status,
               r.progress
        FROM edu_exam e
                 LEFT JOIN edu_user_exam_record r ON e.id = r.exam_id
            AND r.is_newest = 1
            AND r.user_id = #{req.userId}
        WHERE e.deleted = 0
          AND e.type = #{req.type}
          AND e.hsk_level = #{req.hskLevel}
          AND e.publish_status = 1
        ORDER BY e.sort
    </select>
</mapper>