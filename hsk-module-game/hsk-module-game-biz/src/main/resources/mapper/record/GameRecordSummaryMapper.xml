<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.game.dal.mysql.record.GameRecordSummaryMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 批量查询用户在指定专项练习中的最新记录 -->
    <select id="getLatestRecords"
            resultType="com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO">
        SELECT * FROM (
        SELECT id,
               user_id,
               special_exercise_id,
               type,
               total_questions,
               answered_questions,
               correct_questions,
               wrong_questions,
               unanswered_questions,
               answer_start_time,
               answer_end_time,
               progress,
        practice_status,
               create_time,
               update_time,
               deleted,
               ROW_NUMBER() OVER (PARTITION BY special_exercise_id ORDER BY id DESC) as rn
        FROM game_record_summary
        WHERE user_id = #{userId}
          AND special_exercise_id IN
        <foreach collection="specialExerciseIdList" item="specialExerciseId"
                 separator="," open="(" close=")">
            #{specialExerciseId}
        </foreach>
        AND deleted = 0
        ) ranked_records
        WHERE ranked_records.rn = 1
    </select>

    <select id="getLatestByUserIdAndPracticeStatus"
            resultType="com.xt.hsk.module.game.dal.dataobject.record.GameRecordSummaryDO">
        SELECT *
        FROM (SELECT *,
                     ROW_NUMBER() OVER (
                         PARTITION BY user_id, special_exercise_id
                         ORDER BY id DESC
                         ) AS rn
              FROM game_record_summary
              WHERE deleted = 0
                and practice_status = #{practiceStatus}
                AND user_id = #{userId}) AS ranked
        WHERE rn = 1
    </select>
</mapper>