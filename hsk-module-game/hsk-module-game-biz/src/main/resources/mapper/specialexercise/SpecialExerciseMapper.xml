<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.game.dal.mysql.specialexercise.SpecialExerciseMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <!-- 分页查询的条件 -->
    <sql id="specialExerciseConditions">

        <!-- 根据类型决定查询哪个题库 -->
        <choose>
            <!-- 查询单词连连看（type=1）或笔画书写（type=2） -->
            <when test="req.type == 1 or req.type == 2">
                AND EXISTS (
                        SELECT 1
                        FROM game_exercise_question eq
                        JOIN edu_word w ON eq.question_id = w.id
                        WHERE eq.special_exercise_id = se.id
                            AND eq.deleted = 0
                            AND w.deleted = 0
                            AND w.word LIKE CONCAT('%',
                            #{req.question}, '%')
                    )
            </when>

            <!-- 查询连词成句（type=3）或卡拉OK（type=4） -->
            <when test="req.type == 3 or req.type == 4">
                AND EXISTS (
                    SELECT 1
                    FROM game_exercise_question eq
                    JOIN game_question q ON eq.question_id = q.id
                    WHERE eq.special_exercise_id = se.id
                        AND eq.deleted = 0
                        AND q.deleted = 0
                        AND q.question_content LIKE CONCAT('%', #{req.question}, '%')
                )
            </when>

            <!-- 类型为空时，两个表都查 -->
            <otherwise>
                AND (
                    EXISTS (
                        SELECT 1
                        FROM game_exercise_question eq
                        JOIN edu_word w ON eq.question_id = w.id
                        WHERE eq.special_exercise_id = se.id
                            AND se.type IN ( 1, 2 )
                            AND eq.deleted = 0
                            AND w.deleted = 0
                            AND w.word LIKE CONCAT('%', #{req.question}, '%')
                    )
                OR
                    EXISTS (
                        SELECT 1
                        FROM game_exercise_question eq
                        JOIN game_question q ON eq.question_id = q.id
                        WHERE eq.special_exercise_id = se.id
                            AND se.type IN ( 3, 4 )
                            AND eq.deleted = 0
                            AND q.deleted = 0
                            AND q.question_content LIKE CONCAT('%', #{req.question}, '%')
                    )
                )
            </otherwise>
        </choose>

        <!-- 专项练习中文名称 -->
        <if test="req.nameCn != null and req.nameCn != ''">
            AND se.name_cn LIKE CONCAT('%', #{req.nameCn}, '%')
        </if>

        <!-- 专项练习英文名称 -->
        <if test="req.nameEn != null and req.nameEn != ''">
            AND se.name_en LIKE CONCAT('%', #{req.nameEn}, '%')
        </if>

        <!-- 专项练习其他名称 -->
        <if test="req.nameOt != null and req.nameOt != ''">
            AND se.name_ot LIKE CONCAT('%', #{req.nameOt}, '%')
        </if>

        <!-- hsk等级 -->
        <if test="req.hskLevel != null">
            AND se.hsk_level = #{req.hskLevel}
        </if>

        <!-- 难度 -->
        <if test="req.difficultyLevel != null">
            AND se.difficulty_level = #{req.difficultyLevel}
        </if>

        <!-- 是否显示 -->
        <if test="req.isShow != null">
            AND se.is_show = #{req.isShow}
        </if>

        <!-- 类型 -->
        <if test="req.type != null">
            AND se.type = #{req.type}
        </if>

        <!-- 专项练习组ID -->
        <if test="req.specialExerciseId != null">
            AND se.id LIKE CONCAT('%', #{req.specialExerciseId}, '%')
        </if>

        <!-- id列表 -->
        <if test="req.ids != null and req.ids.size() > 0">
            AND se.id IN
            <foreach item="id" collection="req.ids" separator="," close=")" open="(" index="index">
                #{id}
            </foreach>
        </if>

        <!-- 排除的id列表 -->
        <if test="req.excludeIds != null and req.excludeIds.size() > 0">
            AND se.id NOT IN
            <foreach item="id" collection="req.excludeIds" separator="," close=")" open="(" index="index">
                #{id}
            </foreach>
        </if>
    </sql>

    <select id="getSpecialExercisePage" resultType="com.xt.hsk.module.game.dal.dataobject.specialexercise.SpecialExerciseDO">
        SELECT se.*
        FROM game_special_exercise se
        WHERE se.deleted = 0
        <!-- 查询条件 -->
        <include refid="specialExerciseConditions"/>
        ORDER BY se.hsk_level,
                 se.sort
    </select>

    <select id="countSpecialExercisePage" resultType="java.lang.Long">
        SELECT count(*)
        FROM game_special_exercise se
        WHERE se.deleted = 0
        <!-- 查询条件 -->
        <include refid="specialExerciseConditions"/>
    </select>

  <select id="getQuestionInfoList"
    resultType="com.xt.hsk.module.game.controller.admin.specialexercise.vo.ExerciseQuestionInfoRespVO">
    SELECT eq.special_exercise_id,
    eq.question_id,
    eq.translation_ot,
    <choose>
      <when test="type == 1 || type == 2">
        question.word as question_content,
      </when>
      <otherwise>
        question.question_content as question_content,
      </otherwise>
    </choose>
    question.pinyin
    FROM `game_exercise_question` eq
    <choose>
      <when test="type == 1 || type == 2">
        LEFT JOIN edu_word question ON eq.question_id = question.id
      </when>
      <otherwise>
        LEFT JOIN game_question question ON eq.question_id = question.id
      </otherwise>
    </choose>
    WHERE eq.deleted = 0
    AND question.deleted = 0
    AND eq.special_exercise_id IN
    <foreach item="id" collection="idList" separator="," close=")" open="(" index="index">
      #{id}
    </foreach>
  </select>

  <select id="getExerciseQuestion"
    resultType="com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO">
    SELECT eq.special_exercise_id,
    eq.is_show,
    eq.sort,
    eq.show_round,
    eq.round_number,
    eq.translation_ot,
    question.id as question_id,
    <choose>
      <when test="type == 1 || type == 2">
        question.word as question_content,
      </when>
      <otherwise>
        question.question_content as question_content,
      </otherwise>
    </choose>
      question.pinyin,
      eq.create_time
    FROM `game_exercise_question` eq
    <choose>
      <when test="type == 1 || type == 2">
        LEFT JOIN edu_word question ON eq.question_id = question.id
      </when>
      <otherwise>
        LEFT JOIN game_question question ON eq.question_id = question.id
      </otherwise>
    </choose>
    WHERE eq.deleted = 0
    AND question.deleted = 0
    AND eq.special_exercise_id = #{specialExerciseId}
      AND eq.type = #{type}
  </select>
    <select id="getQuestionInfoListByWordId"
            resultType="com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO">
        select gse.id,
               gse.name_cn,
               gse.type,
               gse.hsk_level,
               gse.is_show,
               gse.sort,
               eq.special_exercise_id
        from game_special_exercise gse
                 left join game_exercise_question eq on gse.id = eq.special_exercise_id
        where gse.deleted = 0
          and eq.deleted = 0
          and gse.type in (1, 2)
          and eq.question_id = #{wordId}
    </select>
    <select id="countQuoteByWordIds" resultType="com.xt.hsk.module.game.api.dto.WordQuoteCountDTO">
        select eq.question_id as wordId,
        count(1) as quote_count
        from game_special_exercise gse
        left join game_exercise_question eq on gse.id = eq.special_exercise_id
        where gse.deleted = 0 and eq.deleted = 0 and gse.type in (1,2)
        and eq.question_id in
        <foreach collection="wordIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by eq.question_id
    </select>
    <select id="getSpecialExerciseQuoteInfo"
            resultType="com.xt.hsk.module.game.api.dto.SpecialExercisePageRespDTO">
        select gse.id,
        gse.name_cn,
        gse.type,
        gse.hsk_level,
        gse.is_show,
        eq.sort,
        eq.special_exercise_id,
        eq.translation_ot
        from game_special_exercise gse
        left join game_exercise_question eq on gse.id = eq.special_exercise_id
        where gse.deleted = 0
        and eq.deleted = 0
        and eq.question_id = #{wordId}
        and eq.translation_ot in
        <foreach collection="vis" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAppSpecialExercisePage"
            resultType="com.xt.hsk.module.game.controller.app.specialexercise.vo.SpecialExerciseRecordRespVO">
        SELECT se.id,
               se.id AS special_exercise_id,
               se.name_cn,
               se.name_en,
               se.name_ot,
               se.type,
               se.hsk_level,
               se.difficulty_level,
               rs.id AS record_summary_id,
               rs.practice_status,
               rs.total_questions,
               rs.correct_questions,
               rs.wrong_questions,
               rs.progress
        FROM game_special_exercise se
                 LEFT JOIN game_record_summary rs ON se.id = rs.special_exercise_id
            AND rs.deleted = 0
            AND rs.user_id = #{req.userId}
            AND rs.is_newest = 1
            <!-- 来源 -->
            <if test="req.source != null">
                AND rs.source = #{req.source}
            </if>
        WHERE se.deleted = 0
          AND se.hsk_level = #{req.hskLevel}
          AND se.type = #{req.type}
          AND se.is_show = 1

        <!-- 名称 -->
        <if test="req.name != null and req.name != ''">
            AND (
                se.name_cn LIKE CONCAT('%', #{req.name}, '%')
                    or
                se.name_en LIKE CONCAT('%', #{req.name}, '%')
                    or
                se.name_ot LIKE CONCAT('%', #{req.name}, '%')
                )
        </if>

        <!-- 练习状态 -->
        <if test="req.practiceStatusList != null and req.practiceStatusList.size() > 0">
            AND COALESCE(rs.practice_status, 3) IN
            <foreach item="practiceStatus" collection="req.practiceStatusList" separator="," close=")" open="("
                     index="index">
                #{practiceStatus}
            </foreach>
        </if>

        <!-- 难度 -->
        <if test="req.difficultyLevelList != null and req.difficultyLevelList.size() > 0">
            AND se.difficulty_level IN
            <foreach item="difficultyLevel" collection="req.difficultyLevelList" separator="," close=")" open="("
                     index="index">
                #{difficultyLevel}
            </foreach>
        </if>

        ORDER BY se.sort
    </select>
</mapper>