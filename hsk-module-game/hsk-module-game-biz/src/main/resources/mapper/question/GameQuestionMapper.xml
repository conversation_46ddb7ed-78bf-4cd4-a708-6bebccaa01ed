<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.game.dal.mysql.question.GameQuestionMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="getExerciseReferences"
            resultType="com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionExerciseReferenceVO">
        SELECT t.id AS special_exercise_id,
               t.name_cn,
               t.is_show,
               t.updater,
               t.update_time
        FROM (SELECT sp.id,
                     sp.name_cn,
                     sp.is_show,
                     sp.updater,
                     sp.update_time,
                     ROW_NUMBER() OVER (PARTITION BY sp.id ORDER BY sp.update_time DESC) AS rn
              FROM game_special_exercise sp
                       LEFT JOIN game_exercise_question eq ON sp.id = eq.special_exercise_id
              WHERE sp.deleted = 0
                AND eq.deleted = 0
                AND eq.question_id = #{questionId}
                AND eq.type = #{type}) t
        WHERE t.rn = 1
    </select>
</mapper>