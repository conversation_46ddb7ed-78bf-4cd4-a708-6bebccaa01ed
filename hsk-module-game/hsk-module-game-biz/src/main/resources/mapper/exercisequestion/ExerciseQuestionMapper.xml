<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.game.dal.mysql.exercisequestion.ExerciseQuestionMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="saveBatch" keyProperty="id" useGeneratedKeys="true">
      insert into game_exercise_question(special_exercise_id, question_id, translation_ot, is_show,
      sort, show_round,
                                           round_number, version, type, creator, create_time, updater, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
          (#{entity.specialExerciseId}, #{entity.questionId}, #{entity.translationOt},
          #{entity.isShow},
          #{entity.sort}, #{entity.showRound}, #{entity.roundNumber}, #{entity.version},
          #{entity.type},
          #{entity.creator}, #{entity.createTime}, #{entity.updater}, #{entity.updateTime})
        </foreach>
    </insert>

    <select id="referenceCount"
            resultType="com.xt.hsk.module.game.controller.admin.question.vo.GameQuestionReferenceCountVO">
        SELECT question_id,
        COUNT(special_exercise_id) AS reference_count
        FROM game_exercise_question
        WHERE deleted = 0
        AND type = #{type}
        AND question_id IN
        <foreach item="questionId" collection="questionIdList" separator="," open="(" close=")" index="">
            #{questionId}
        </foreach>
        GROUP BY question_id
    </select>
    <select id="isQuoteWord" resultType="java.lang.Integer">
        select count(1)
        from game_special_exercise gse
                 left join game_exercise_question geq on gse.id = geq.special_exercise_id
        where gse.deleted = 0
          and geq.deleted = 0
          and geq.question_id = #{wordId}
    </select>
</mapper>