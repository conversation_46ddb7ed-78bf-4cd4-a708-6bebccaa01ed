<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xt.hsk.module.game.dal.mysql.exercisequestion.ExerciseQuestionVersionMapper">
    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <insert id="saveBatch" keyProperty="id" useGeneratedKeys="true">
      insert into game_exercise_question_version(exercise_question_id, special_exercise_id,
      question_id,
                                                   translation_ot, is_show, sort, show_round, round_number, version,
                                                   type, creator, create_time, updater, update_time)
        values
        <foreach collection="entities" item="entity" separator=",">
          (#{entity.exerciseQuestionId}, #{entity.specialExerciseId}, #{entity.questionId},
          #{entity.translationOt},
          #{entity.isShow}, #{entity.sort}, #{entity.showRound}, #{entity.roundNumber},
          #{entity.version},
          #{entity.type}, #{entity.creator}, #{entity.createTime}, #{entity.updater},
          #{entity.updateTime})
        </foreach>
    </insert>

    <select id="getExerciseQuestion"
            resultType="com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO">
        SELECT eqv.id AS exercise_question_version_id,
               eqv.exercise_question_id,
               eqv.special_exercise_id,
               eqv.is_show,
               eqv.sort,
               eqv.show_round,
               eqv.round_number,
               eqv.translation_ot,
               question.id as question_id,
                <choose>
                    <when test="type == 1 || type == 2">
                        question.word as question_content,
                        question.audio_url,
                    </when>
                    <otherwise>
                        question.question_content as question_content,
                        question.question_split,
                        question.audio_url,
                        question.knowledge_point,
                        question.knowledge_point_pinyin,
                        question.reference_answer,
                    </otherwise>
                </choose>
                question.pinyin,
                eqv.create_time
        FROM `game_exercise_question_version` eqv
        <choose>
            <when test="type == 1 || type == 2">
                LEFT JOIN edu_word question ON eqv.question_id = question.id
            </when>
            <otherwise>
                LEFT JOIN game_question question ON eqv.question_id = question.id
            </otherwise>
        </choose>
        WHERE eqv.deleted = 0
          AND question.deleted = 0
          AND eqv.special_exercise_id = #{specialExerciseId}
          AND eqv.type = #{type}
          AND eqv.version = #{version}
            <choose>
                <when test="type == 1">
                    AND eqv.show_round = 1
                </when>
                <otherwise>
                    AND eqv.is_show = 1
                </otherwise>
            </choose>
        <choose>
            <when test="type == 1">
                ORDER BY eqv.round_number
            </when>
            <otherwise>
                ORDER BY eqv.sort
            </otherwise>
        </choose>



    </select>

    <select id="getExerciseQuestionById"
            resultType="com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO">
        SELECT eqv.id AS exercise_question_version_id,
               eqv.exercise_question_id,
               eqv.special_exercise_id,
               eqv.is_show,
               eqv.sort,
               eqv.show_round,
               eqv.round_number,
               eqv.translation_ot,
               question.id as question_id,
                <choose>
                    <when test="type == 1 || type == 2">
                        question.word as question_content,
                    </when>
                    <otherwise>
                        question.question_content as question_content,
                        question.question_split,
                        question.audio_url,
                        question.knowledge_point,
                        question.reference_answer,
                    </otherwise>
                </choose>
               question.pinyin,
               eqv.create_time
        FROM `game_exercise_question_version` eqv
        <choose>
            <when test="type == 1 || type == 2">
                LEFT JOIN edu_word question ON eqv.question_id = question.id
            </when>
            <otherwise>
                LEFT JOIN game_question question ON eqv.question_id = question.id
            </otherwise>
        </choose>
        WHERE eqv.deleted = 0
            AND question.deleted = 0
            AND eqv.type = #{type}
            AND eqv.id = #{id}
    </select>

    <select id="getExerciseQuestionByIdList"
            resultType="com.xt.hsk.module.game.controller.admin.exercisequestion.vo.ExerciseQuestionRespVO">
        SELECT eqv.id      AS exercise_question_version_id,
               eqv.exercise_question_id,
               eqv.special_exercise_id,
               eqv.is_show,
               eqv.sort,
               eqv.show_round,
               eqv.round_number,
               eqv.translation_ot,
               question.id as question_id,
        <choose>
            <when test="type == 1 || type == 2">
                question.word as question_content,
            </when>
            <otherwise>
                question.question_content as question_content,
                question.question_split,
                question.audio_url,
                question.knowledge_point,
                question.reference_answer,
            </otherwise>
        </choose>
        question.pinyin,
        eqv.create_time
        FROM `game_exercise_question_version` eqv
        <choose>
            <when test="type == 1 || type == 2">
                LEFT JOIN edu_word question ON eqv.question_id = question.id
            </when>
            <otherwise>
                LEFT JOIN game_question question ON eqv.question_id = question.id
            </otherwise>
        </choose>
        WHERE eqv.deleted = 0
          AND question.deleted = 0
          AND eqv.type = #{type}
          AND eqv.id IN
        <foreach item="id" collection="exerciseQuestionVersionIdList" separator="," open="(" close=")">
            #{id}
        </foreach>
    </select>
</mapper>