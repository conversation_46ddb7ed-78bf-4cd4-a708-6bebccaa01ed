package com.xt.hsk.module.thirdparty.service;

import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 讯飞翻译功能测试
 */
@Slf4j
@SpringBootTest
public class XunFeiTranslateTest {

    @Autowired
    private XunFeiServiceImpl xunFeiService;
    
    @Autowired
    private XunFeiConfig xunFeiConfig;

    @Test
    public void testTranslate() {
        log.info("开始测试讯飞翻译功能");
        log.info("配置信息: appId={}, accessKey={}", xunFeiConfig.getAppId(), xunFeiConfig.getAccessKey());
        
        String text = "中华人民共和国于1949年成立";
        String from = "cn";
        String to = "en";
        
        try {
            String result = xunFeiService.translate(text, from, to);
            log.info("翻译结果: {}", result);
            
            if (result != null && !result.isEmpty()) {
                log.info("翻译成功!");
            } else {
                log.error("翻译失败，返回结果为空");
            }
        } catch (Exception e) {
            log.error("翻译过程中发生异常", e);
        }
    }

    @Test
    public void testBuildRequestBody() {
        try {
            // 使用反射调用私有方法测试请求体构建
            java.lang.reflect.Method method = XunFeiServiceImpl.class.getDeclaredMethod(
                    "buildTranslateRequestBody", String.class, String.class, String.class);
            method.setAccessible(true);

            String requestBodyStr = (String) method.invoke(xunFeiService, "你好世界", "cn", "en");
            log.info("构建的请求体: {}", requestBodyStr);

            // 验证请求体结构
            JSONObject requestBody = JSONObject.parseObject(requestBodyStr);
            assert requestBody.containsKey("common");
            assert requestBody.containsKey("business");
            assert requestBody.containsKey("data");

            JSONObject common = requestBody.getJSONObject("common");
            assert common.containsKey("app_id");

            JSONObject business = requestBody.getJSONObject("business");
            assert business.containsKey("from");
            assert business.containsKey("to");

            JSONObject data = requestBody.getJSONObject("data");
            assert data.containsKey("text");

            log.info("请求体结构验证通过");
        } catch (Exception e) {
            log.error("测试请求体构建失败", e);
        }
    }
}
