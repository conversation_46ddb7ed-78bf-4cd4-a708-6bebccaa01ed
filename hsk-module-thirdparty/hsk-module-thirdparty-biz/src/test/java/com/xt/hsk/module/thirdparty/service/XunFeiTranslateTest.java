package com.xt.hsk.module.thirdparty.service;

import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * 讯飞翻译功能测试
 */
@Slf4j
@SpringBootTest
public class XunFeiTranslateTest {

    @Autowired
    private XunFeiServiceImpl xunFeiService;
    
    @Autowired
    private XunFeiConfig xunFeiConfig;

    @Test
    public void testTranslate() {
        log.info("开始测试讯飞翻译功能");
        log.info("配置信息: appId={}, accessKey={}", xunFeiConfig.getAppId(), xunFeiConfig.getAccessKey());
        
        String text = "中华人民共和国于1949年成立";
        String from = "cn";
        String to = "en";
        
        try {
            String result = xunFeiService.translate(text, from, to);
            log.info("翻译结果: {}", result);
            
            if (result != null && !result.isEmpty()) {
                log.info("翻译成功!");
            } else {
                log.error("翻译失败，返回结果为空");
            }
        } catch (Exception e) {
            log.error("翻译过程中发生异常", e);
        }
    }

    @Test
    public void testFormUrlEncodedValueParameters() {
        try {
            // 使用反射调用私有方法测试URL参数构建
            java.lang.reflect.Method method = XunFeiServiceImpl.class.getDeclaredMethod(
                    "formUrlEncodedValueParameters", java.util.Map.class);
            method.setAccessible(true);

            java.util.Map<String, Object> params = new java.util.TreeMap<>();
            params.put("appId", "test_app_id");
            params.put("accessKeyId", "test_access_key");
            params.put("uuid", "test_uuid");

            String urlParams = (String) method.invoke(xunFeiService, params);
            log.info("构建的URL参数: {}", urlParams);

            // 验证URL参数格式
            assert urlParams.contains("appId=test_app_id");
            assert urlParams.contains("accessKeyId=test_access_key");
            assert urlParams.contains("uuid=test_uuid");

            log.info("URL参数构建验证通过");
        } catch (Exception e) {
            log.error("测试URL参数构建失败", e);
        }
    }
}
