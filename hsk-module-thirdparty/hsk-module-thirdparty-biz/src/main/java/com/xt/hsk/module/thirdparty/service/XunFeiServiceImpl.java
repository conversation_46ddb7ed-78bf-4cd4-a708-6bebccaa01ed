package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateRequestDto;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateResponseDto;
import com.xt.hsk.module.thirdparty.utils.FileUtils;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FrameGrabber;
import org.springframework.stereotype.Service;

/**
 * 讯飞语音接口
 */
@Slf4j
@Service
public class XunFeiServiceImpl implements XunFeiApi {
    @Resource
    private XunFeiConfig xunFeiConfig;
    private static final String SERVICE_URL = "https://api.iflyrec.com";
    private static String SERVICE_URL_UPLOAD = SERVICE_URL + "/v2/upload";
    private static String SERVICE_URL_GET_RESULT = SERVICE_URL + "/v2/getResult";

    // 讯飞翻译API地址
    private static final String TRANSLATE_URL = "https://itrans.xfyun.cn/v2/its";

    @Override
    public String upload(String fileUrl, String callbackUrl) {
        FFmpegFrameGrabber grabber = null;
        InputStream inputStream = null;
        File file = null;
        try {
            String encode = FileUtils.setUrL(fileUrl);
            URL url = new URL(encode);
            inputStream = url.openStream();
            file = File.createTempFile("temp", ".mp3");
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            String fileName = file.getName();
            long fileSize = file.length();
            Map<String, Object> map = new TreeMap<String, Object>();
            map.put("fileName", fileName);
            map.put("fileSize", fileSize);
            grabber = FFmpegFrameGrabber.createDefault(encode);
            grabber.start();
            map.put("duration", grabber.getLengthInTime() / (1000));//真实的音频时长
            map.put("roleType", 1);
            map.put("callbackUrl", xunFeiConfig.getCallbackUrlBase() + callbackUrl);
            map.put("transMode", 1);
            map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
            map.put("accessKeyId", xunFeiConfig.getAccessKey());
            map.put("signatureRandom", UUID.randomUUID().toString());
            map.put("language", "cn");


            String string = requestPost(SERVICE_URL_UPLOAD, map, Files.readAllBytes(file.toPath()));
            log.info("上传结果:{}", string);


        } catch (Exception e) {
            log.warn("系统异常 讯飞题目音转写:", e);
        } finally {
            deleteFile(file, inputStream, grabber);
        }
        return "";
    }

    @Override
    public String getResult(String orderId) {
        // ☆☆☆使用TreeMap对内容根据Key进行自然排序
        Map<String, Object> map = new TreeMap<String, Object>();
        map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
        map.put("signatureRandom", UUID.randomUUID().toString());
        map.put("accessKeyId", xunFeiConfig.getAccessKey());
        map.put("orderId", orderId);//订单ID
        String result = null;
        try {
            result = requestGet(SERVICE_URL_GET_RESULT, map);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        String res = "";
        if (result != null) {
            // 解析中文字符串
            res = parsingChinese(result);
        }
        return res;
    }

    private String parsingChinese(String result) {
        try {
            // 1. 解析最外层JSON
            JSONObject root = JSONObject.parseObject(result);
            JSONObject content = root.getJSONObject("content");
//            JSONObject orderInfo = content.getJSONObject("orderResult");

            // 2. 获取orderResult字符串并解析为JSON对象
            String orderResultStr = content.getString("orderResult");
            JSONObject orderResultJson = JSONObject.parseObject(orderResultStr);

            // 3. 提取lattice数组的第一个元素
            JSONArray lattice = orderResultJson.getJSONArray("lattice");
            JSONObject firstLattice = lattice.getJSONObject(0);

            // 4. 解析json_1best字符串
            String json1bestStr = firstLattice.getString("json_1best");
            JSONObject json1best = JSONObject.parseObject(json1bestStr);
            JSONObject st = json1best.getJSONObject("st");

            // 5. 遍历rt[0].ws数组拼接中文文本
            JSONArray rt = st.getJSONArray("rt");
            JSONObject firstRt = rt.getJSONObject(0);
            JSONArray ws = firstRt.getJSONArray("ws");

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < ws.size(); i++) {
                JSONObject wordSeg = ws.getJSONObject(i);
                JSONArray cw = wordSeg.getJSONArray("cw");
                JSONObject word = cw.getJSONObject(0);
                String w = word.getString("w");
                // 跳过空字符串
                if (!w.isEmpty()) {
                    sb.append(w);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("解析讯飞结果失败:{}", e.getMessage(), e);
        }
        return null;
    }

    private String requestPost(String url, Map<String, Object> map,
                               byte[] uploadContent) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        String uploadUrl = url + "?" + formUrlEncodedString.toString();
        HttpRequest request = HttpRequest.post(uploadUrl);
        request.header("signature", signature);
        request.header("application/json", "UTF-8");
        request.body(uploadContent);
        HttpResponse execute = request.execute();
        int statusCode = execute.getStatus();
        if (statusCode != 200) {
            String message = "call servie failed: "
                    + execute.getStatus();
            log.error(message);
        }
        String body = execute.body();
        return body;
    }

    private String requestGet(String url, Map<String, Object> map) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        HttpRequest httpRequest = HttpRequest.get(url + "?" + formUrlEncodedString.toString());
        httpRequest.header("signature", signature);
        HttpResponse execute = httpRequest.execute();
        return execute.body();
    }

    private void deleteFile(File file, InputStream inputStream, FFmpegFrameGrabber grabber) {
        if (file != null) {
            file.delete();
        }

        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
            }
        }

        if (grabber != null) {
            try {
                grabber.stop();
            } catch (FrameGrabber.Exception e) {
            }
        }
    }


    private String getXunFeiSign(String formUrlEncodedString) throws Exception {

        byte[] signBytes = hmacSHA1Signature(xunFeiConfig.getAccessSecret(), formUrlEncodedString);

        return Base64.getEncoder().encodeToString(signBytes);

    }


    public static byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StrUtil.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StrUtil.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8),
                "UTF-8");
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public String translate(String text, String from, String to) {
        if (StrUtil.isEmpty(text)) {
            log.warn("翻译文本为空");
            return "";
        }

        try {
            XunFeiTranslateRequestDto requestDto = new XunFeiTranslateRequestDto(text, from, to);
            XunFeiTranslateResponseDto responseDto = doTranslate(requestDto);

            if (responseDto.isSuccess()) {
                return responseDto.getResult();
            } else {
                log.error("讯飞翻译失败: code={}, message={}", responseDto.getCode(), responseDto.getMessage());
                return "";
            }
        } catch (Exception e) {
            log.error("讯飞翻译异常: text={}, from={}, to={}", text, from, to, e);
            return "";
        }
    }

    /**
     * 执行翻译请求
     */
    private XunFeiTranslateResponseDto doTranslate(XunFeiTranslateRequestDto requestDto) throws Exception {
        // 构建请求参数
        Map<String, Object> params = buildTranslateParams(requestDto);

        // 构建请求体
        JSONObject requestBody = new JSONObject();
        requestBody.put("text", requestDto.getText());

        // 发送请求
        String response = requestTranslatePost(TRANSLATE_URL, params, requestBody.toJSONString());

        log.info("翻译响应:{}", response);
        // 解析响应
        return parseTranslateResponse(response);
    }

    /**
     * 构建翻译请求参数
     */
    private Map<String, Object> buildTranslateParams(XunFeiTranslateRequestDto requestDto) {
        Map<String, Object> params = new TreeMap<>();
        params.put("appid", xunFeiConfig.getAppId());
        params.put("from", requestDto.getFrom());
        params.put("to", requestDto.getTo());
        params.put("salt", System.currentTimeMillis());

        return params;
    }


    /**
     * 发送翻译POST请求
     */
    private String requestTranslatePost(String url, Map<String, Object> params, String requestBody) throws Exception {
        // 构建查询字符串
        StringBuilder queryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() != null) {
                queryString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()), StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (queryString.length() > 1) {
            queryString.setLength(queryString.length() - 1);
        }

        // 生成签名
        String signString = queryString.toString() + requestBody;
        String sign = getXunFeiSign(signString);

        // 构建完整URL
        String fullUrl = url + "?" + queryString.toString() + "&sign=" + URLEncoder.encode(sign, StandardCharsets.UTF_8);

        // 发送请求
        HttpRequest request = HttpRequest.post(fullUrl);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.header("Authorization", "api_key="+xunFeiConfig.getAccessKey()+", algorithm='hmac-sha256', headers="'itrans.xfyun.cn'"+" date request-line digest"+", signature="+sign);
        request.body(requestBody);

        log.info("翻译请求:{}", request);
        HttpResponse response = request.execute();

        if (response.getStatus() != 200) {
            throw new RuntimeException("翻译请求失败: " + response.getStatus());
        }

        return response.body();
    }


    /**
     * 解析翻译响应
     */
    private XunFeiTranslateResponseDto parseTranslateResponse(String response) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(response);

            XunFeiTranslateResponseDto responseDto = new XunFeiTranslateResponseDto();
            responseDto.setCode(jsonResponse.getInteger("code"));
            responseDto.setMessage(jsonResponse.getString("message"));
            responseDto.setSid(jsonResponse.getString("sid"));

            // 解析翻译结果
            if (jsonResponse.containsKey("data") && jsonResponse.getJSONObject("data") != null) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data.containsKey("result") && data.getJSONObject("result") != null) {
                    JSONObject result = data.getJSONObject("result");
                    if (result.containsKey("trans_result") && result.getJSONArray("trans_result") != null) {
                        JSONArray transResult = result.getJSONArray("trans_result");
                        if (transResult.size() > 0) {
                            JSONObject firstResult = transResult.getJSONObject(0);
                            responseDto.setResult(firstResult.getString("dst"));
                        }
                    }
                }
            }

            return responseDto;
        } catch (Exception e) {
            log.error("解析翻译响应失败: {}", response, e);
            XunFeiTranslateResponseDto errorResponse = new XunFeiTranslateResponseDto();
            errorResponse.setCode(-1);
            errorResponse.setMessage("解析响应失败");
            return errorResponse;
        }
    }
}
