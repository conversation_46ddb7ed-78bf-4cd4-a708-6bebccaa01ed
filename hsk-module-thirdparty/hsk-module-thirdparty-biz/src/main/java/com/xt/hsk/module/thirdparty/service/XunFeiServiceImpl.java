package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;

import com.xt.hsk.module.thirdparty.utils.FileUtils;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FrameGrabber;
import org.springframework.stereotype.Service;

/**
 * 讯飞语音接口
 */
@Slf4j
@Service
public class XunFeiServiceImpl implements XunFeiApi {
    @Resource
    private XunFeiConfig xunFeiConfig;
    private static final String SERVICE_URL = "https://api.iflyrec.com";
    private static String SERVICE_URL_UPLOAD = SERVICE_URL + "/v2/upload";
    private static String SERVICE_URL_GET_RESULT = SERVICE_URL + "/v2/getResult";

    // 讯飞翻译API地址
    private static final String TRANSLATE_URL = SERVICE_URL+"/trans/v2/chapt_trans";

    @Override
    public String upload(String fileUrl, String callbackUrl) {
        FFmpegFrameGrabber grabber = null;
        InputStream inputStream = null;
        File file = null;
        try {
            String encode = FileUtils.setUrL(fileUrl);
            URL url = new URL(encode);
            inputStream = url.openStream();
            file = File.createTempFile("temp", ".mp3");
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            String fileName = file.getName();
            long fileSize = file.length();
            Map<String, Object> map = new TreeMap<String, Object>();
            map.put("fileName", fileName);
            map.put("fileSize", fileSize);
            grabber = FFmpegFrameGrabber.createDefault(encode);
            grabber.start();
            map.put("duration", grabber.getLengthInTime() / (1000));//真实的音频时长
            map.put("roleType", 1);
            map.put("callbackUrl", xunFeiConfig.getCallbackUrlBase() + callbackUrl);
            map.put("transMode", 1);
            map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
            map.put("accessKeyId", xunFeiConfig.getAccessKey());
            map.put("signatureRandom", UUID.randomUUID().toString());
            map.put("language", "cn");


            String string = requestPost(SERVICE_URL_UPLOAD, map, Files.readAllBytes(file.toPath()));
            log.info("上传结果:{}", string);


        } catch (Exception e) {
            log.warn("系统异常 讯飞题目音转写:", e);
        } finally {
            deleteFile(file, inputStream, grabber);
        }
        return "";
    }

    @Override
    public String getResult(String orderId) {
        // ☆☆☆使用TreeMap对内容根据Key进行自然排序
        Map<String, Object> map = new TreeMap<String, Object>();
        map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
        map.put("signatureRandom", UUID.randomUUID().toString());
        map.put("accessKeyId", xunFeiConfig.getAccessKey());
        map.put("orderId", orderId);//订单ID
        String result = null;
        try {
            result = requestGet(SERVICE_URL_GET_RESULT, map);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        String res = "";
        if (result != null) {
            // 解析中文字符串
            res = parsingChinese(result);
        }
        return res;
    }

    private String parsingChinese(String result) {
        try {
            // 1. 解析最外层JSON
            JSONObject root = JSONObject.parseObject(result);
            JSONObject content = root.getJSONObject("content");
//            JSONObject orderInfo = content.getJSONObject("orderResult");

            // 2. 获取orderResult字符串并解析为JSON对象
            String orderResultStr = content.getString("orderResult");
            JSONObject orderResultJson = JSONObject.parseObject(orderResultStr);

            // 3. 提取lattice数组的第一个元素
            JSONArray lattice = orderResultJson.getJSONArray("lattice");
            JSONObject firstLattice = lattice.getJSONObject(0);

            // 4. 解析json_1best字符串
            String json1bestStr = firstLattice.getString("json_1best");
            JSONObject json1best = JSONObject.parseObject(json1bestStr);
            JSONObject st = json1best.getJSONObject("st");

            // 5. 遍历rt[0].ws数组拼接中文文本
            JSONArray rt = st.getJSONArray("rt");
            JSONObject firstRt = rt.getJSONObject(0);
            JSONArray ws = firstRt.getJSONArray("ws");

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < ws.size(); i++) {
                JSONObject wordSeg = ws.getJSONObject(i);
                JSONArray cw = wordSeg.getJSONArray("cw");
                JSONObject word = cw.getJSONObject(0);
                String w = word.getString("w");
                // 跳过空字符串
                if (!w.isEmpty()) {
                    sb.append(w);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("解析讯飞结果失败:{}", e.getMessage(), e);
        }
        return null;
    }

    private String requestPost(String url, Map<String, Object> map,
                               byte[] uploadContent) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        String uploadUrl = url + "?" + formUrlEncodedString.toString();
        HttpRequest request = HttpRequest.post(uploadUrl);
        request.header("signature", signature);
        request.header("application/json", "UTF-8");
        request.body(uploadContent);
        HttpResponse execute = request.execute();
        int statusCode = execute.getStatus();
        if (statusCode != 200) {
            String message = "call servie failed: "
                    + execute.getStatus();
            log.error(message);
        }
        String body = execute.body();
        return body;
    }

    private String requestGet(String url, Map<String, Object> map) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        HttpRequest httpRequest = HttpRequest.get(url + "?" + formUrlEncodedString.toString());
        httpRequest.header("signature", signature);
        HttpResponse execute = httpRequest.execute();
        return execute.body();
    }

    private void deleteFile(File file, InputStream inputStream, FFmpegFrameGrabber grabber) {
        if (file != null) {
            file.delete();
        }

        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
            }
        }

        if (grabber != null) {
            try {
                grabber.stop();
            } catch (FrameGrabber.Exception e) {
            }
        }
    }


    private String getXunFeiSign(String formUrlEncodedString) throws Exception {

        byte[] signBytes = hmacSHA1Signature(xunFeiConfig.getAccessSecret(), formUrlEncodedString);

        return Base64.getEncoder().encodeToString(signBytes);

    }


    public static byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StrUtil.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StrUtil.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8),
                "UTF-8");
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public String translate(String text, String from, String to) {
        if (StrUtil.isEmpty(text)) {
            log.warn("翻译文本为空");
            return "";
        }

        try {
            // ☆☆☆使用TreeMap对内容根据Key进行自然排序
            Map<String, Object> map = new TreeMap<>();
            map.put("utc", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
            map.put("accessKeyId", xunFeiConfig.getAccessKey());
            map.put("appId", xunFeiConfig.getAppId());
            map.put("uuid", UUID.randomUUID().toString());

            // 生成签名（按照NRTSignature方式）
            String signature = generateSignature(map);
            map.put("signature", signature);

            // 构建URL参数
            String formUrlString = formUrlEncodedValueParameters(map);

            // 设置翻译参数（这些参数不参与URL签名，只在请求体中）
            map.put("from", from);
            map.put("to", to);
            map.put("content", text);
            map.put("transMode", 1);

            // 发送请求
            String result = requestTranslatePost(TRANSLATE_URL + "?" + formUrlString, map);

            // 检查响应
            JSONObject resultJson = JSONObject.parseObject(result);
            if (!Objects.equals(resultJson.get("code"), "000000")) {
                log.warn("系统异常，翻译失败！code：{}", resultJson.get("code"));
                return "";
            }

            // 解析翻译结果
            return parseTranslateResult(result);

        } catch (Exception e) {
            log.error("讯飞翻译异常: text={}, from={}, to={}", text, from, to, e);
            return "";
        }
    }

    /**
     * 发送翻译POST请求（按照requestPost方法修改）
     */
    private String requestTranslatePost(String url, Map<String, Object> map) {
        // 从map中获取已生成的签名
        String signature = (String) map.get("signature");

        // 构建请求体 - 只包含翻译相关参数
        JSONObject requestBody = new JSONObject();
        requestBody.put("from", map.get("from"));
        requestBody.put("to", map.get("to"));
        requestBody.put("content", map.get("content"));
        requestBody.put("transMode", map.get("transMode"));

        // 使用Hutool发送请求，保持与现有代码一致
        HttpRequest request = HttpRequest.post(url);
        request.header("signature", signature);
        request.header("Content-Type", "application/json; charset=utf-8");
        request.body(requestBody.toJSONString());

        log.info("翻译请求URL: {}", url);
        log.info("翻译请求体: {}", requestBody.toJSONString());
        log.info("翻译请求签名: {}", signature);

        HttpResponse response = request.execute();

        int statusCode = response.getStatus();
        if (statusCode != 200) {
            String message = "call service failed: " + statusCode;
            log.error(message);
            return "";
        }

        String responseBody = response.body();
        log.info("翻译响应: {}", responseBody);
        return responseBody;
    }


    /**
     * 解析翻译结果
     */
    private String parseTranslateResult(String result) {
        try {
            JSONObject resultJson = JSONObject.parseObject(result);

            // 检查是否有翻译结果
            if (resultJson.containsKey("data") && resultJson.getJSONObject("data") != null) {
                JSONObject data = resultJson.getJSONObject("data");
                if (data.containsKey("result") && data.getJSONArray("result") != null) {
                    JSONArray resultArray = data.getJSONArray("result");
                    if (resultArray.size() > 0) {
                        JSONObject firstResult = resultArray.getJSONObject(0);
                        if (firstResult.containsKey("trans_result")) {
                            return firstResult.getString("trans_result");
                        }
                    }
                }
            }

            log.warn("翻译响应中未找到翻译结果");
            return "";
        } catch (Exception e) {
            log.error("解析翻译结果失败: {}", result, e);
            return "";
        }
    }






    /**
     * 构建URL编码参数字符串（按照NRTSignature.formUrlEncodedParameters方式）
     */
    private String formUrlEncodedParameters(Map<String, Object> params) throws UnsupportedEncodingException {
        StringBuilder canonicalizedQueryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() != null) {
                canonicalizedQueryString
                        .append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8))
                        .append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()), StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (canonicalizedQueryString.length() > 1) {
            canonicalizedQueryString.setLength(canonicalizedQueryString.length() - 1);
        }
        return canonicalizedQueryString.toString();
    }

    /**
     * 构建URL编码参数字符串（按照NRTSignature.formUrlEncodedValueParameters方式）
     */
    private String formUrlEncodedValueParameters(Map<String, Object> params) throws UnsupportedEncodingException {
        StringBuilder canonicalizedQueryString = new StringBuilder();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            if (entry.getValue() != null) {
                canonicalizedQueryString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()), StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (canonicalizedQueryString.length() > 1) {
            canonicalizedQueryString.setLength(canonicalizedQueryString.length() - 1);
        }
        return canonicalizedQueryString.toString();
    }

    /**
     * 生成签名（按照NRTSignature.gernerateSignature方式）
     */
    private String generateSignature(Map<String, Object> map) throws Exception {
        String signature = "";
        try {
            String formUrlEncodedString = formUrlEncodedParameters(map);
            byte[] signBytes = hmacSHA1Signature(xunFeiConfig.getAccessSecret(), formUrlEncodedString);
            signature = newStringByBase64(signBytes);
        } catch (UnsupportedEncodingException e) {
            log.error("生成签名失败", e);
            signature = "";
        }
        return signature;
    }

    /**
     * HMAC-SHA1签名（按照NRTSignature方式）
     */
    private byte[] hmacSHA1Signature(String secret, String baseString) throws Exception {
        if (StrUtil.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StrUtil.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "UTF-8");
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * Base64编码（按照NRTSignature方式）
     */
    private String newStringByBase64(byte[] bytes) throws UnsupportedEncodingException {
        if (bytes == null || bytes.length == 0) {
            return null;
        }
        return Base64.getEncoder().encodeToString(bytes);
    }
}
