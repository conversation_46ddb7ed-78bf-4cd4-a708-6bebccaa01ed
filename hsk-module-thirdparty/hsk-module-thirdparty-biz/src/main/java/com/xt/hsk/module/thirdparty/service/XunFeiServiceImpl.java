package com.xt.hsk.module.thirdparty.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import com.xt.hsk.module.thirdparty.config.xunfei.XunFeiConfig;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateRequestDto;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateResponseDto;
import com.xt.hsk.module.thirdparty.utils.FileUtils;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;
import java.util.UUID;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FrameGrabber;
import org.springframework.stereotype.Service;

/**
 * 讯飞语音接口
 */
@Slf4j
@Service
public class XunFeiServiceImpl implements XunFeiApi {
    @Resource
    private XunFeiConfig xunFeiConfig;
    private static final String SERVICE_URL = "https://api.iflyrec.com";
    private static String SERVICE_URL_UPLOAD = SERVICE_URL + "/v2/upload";
    private static String SERVICE_URL_GET_RESULT = SERVICE_URL + "/v2/getResult";

    // 讯飞翻译API地址
    private static final String TRANSLATE_URL = SERVICE_URL +"/trans/v2/chapt_trans";

    @Override
    public String upload(String fileUrl, String callbackUrl) {
        FFmpegFrameGrabber grabber = null;
        InputStream inputStream = null;
        File file = null;
        try {
            String encode = FileUtils.setUrL(fileUrl);
            URL url = new URL(encode);
            inputStream = url.openStream();
            file = File.createTempFile("temp", ".mp3");
            try (FileOutputStream fos = new FileOutputStream(file)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            String fileName = file.getName();
            long fileSize = file.length();
            Map<String, Object> map = new TreeMap<String, Object>();
            map.put("fileName", fileName);
            map.put("fileSize", fileSize);
            grabber = FFmpegFrameGrabber.createDefault(encode);
            grabber.start();
            map.put("duration", grabber.getLengthInTime() / (1000));//真实的音频时长
            map.put("roleType", 1);
            map.put("callbackUrl", xunFeiConfig.getCallbackUrlBase() + callbackUrl);
            map.put("transMode", 1);
            map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
            map.put("accessKeyId", xunFeiConfig.getAccessKey());
            map.put("signatureRandom", UUID.randomUUID().toString());
            map.put("language", "cn");


            String string = requestPost(SERVICE_URL_UPLOAD, map, Files.readAllBytes(file.toPath()));
            log.info("上传结果:{}", string);


        } catch (Exception e) {
            log.warn("系统异常 讯飞题目音转写:", e);
        } finally {
            deleteFile(file, inputStream, grabber);
        }
        return "";
    }

    @Override
    public String getResult(String orderId) {
        // ☆☆☆使用TreeMap对内容根据Key进行自然排序
        Map<String, Object> map = new TreeMap<String, Object>();
        map.put("dateTime", new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ").format(new Date()));
        map.put("signatureRandom", UUID.randomUUID().toString());
        map.put("accessKeyId", xunFeiConfig.getAccessKey());
        map.put("orderId", orderId);//订单ID
        String result = null;
        try {
            result = requestGet(SERVICE_URL_GET_RESULT, map);
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        String res = "";
        if (result != null) {
            // 解析中文字符串
            res = parsingChinese(result);
        }
        return res;
    }

    private String parsingChinese(String result) {
        try {
            // 1. 解析最外层JSON
            JSONObject root = JSONObject.parseObject(result);
            JSONObject content = root.getJSONObject("content");
//            JSONObject orderInfo = content.getJSONObject("orderResult");

            // 2. 获取orderResult字符串并解析为JSON对象
            String orderResultStr = content.getString("orderResult");
            JSONObject orderResultJson = JSONObject.parseObject(orderResultStr);

            // 3. 提取lattice数组的第一个元素
            JSONArray lattice = orderResultJson.getJSONArray("lattice");
            JSONObject firstLattice = lattice.getJSONObject(0);

            // 4. 解析json_1best字符串
            String json1bestStr = firstLattice.getString("json_1best");
            JSONObject json1best = JSONObject.parseObject(json1bestStr);
            JSONObject st = json1best.getJSONObject("st");

            // 5. 遍历rt[0].ws数组拼接中文文本
            JSONArray rt = st.getJSONArray("rt");
            JSONObject firstRt = rt.getJSONObject(0);
            JSONArray ws = firstRt.getJSONArray("ws");

            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < ws.size(); i++) {
                JSONObject wordSeg = ws.getJSONObject(i);
                JSONArray cw = wordSeg.getJSONArray("cw");
                JSONObject word = cw.getJSONObject(0);
                String w = word.getString("w");
                // 跳过空字符串
                if (!w.isEmpty()) {
                    sb.append(w);
                }
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("解析讯飞结果失败:{}", e.getMessage(), e);
        }
        return null;
    }

    private String requestPost(String url, Map<String, Object> map,
                               byte[] uploadContent) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        String uploadUrl = url + "?" + formUrlEncodedString.toString();
        HttpRequest request = HttpRequest.post(uploadUrl);
        request.header("signature", signature);
        request.header("application/json", "UTF-8");
        request.body(uploadContent);
        HttpResponse execute = request.execute();
        int statusCode = execute.getStatus();
        if (statusCode != 200) {
            String message = "call servie failed: "
                    + execute.getStatus();
            log.error(message);
        }
        String body = execute.body();
        return body;
    }

    private String requestGet(String url, Map<String, Object> map) throws Exception {
        StringBuilder formUrlEncodedString = new StringBuilder();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (entry.getValue() != null) {
                formUrlEncodedString.append(entry.getKey()).append("=")
                        .append(URLEncoder.encode(String.valueOf(entry.getValue()),
                                StandardCharsets.UTF_8))
                        .append("&");
            }
        }
        if (formUrlEncodedString.length() > 1) {
            formUrlEncodedString
                    .setLength(formUrlEncodedString.length() - 1);
        }
        String signature = getXunFeiSign(formUrlEncodedString.toString());
        HttpRequest httpRequest = HttpRequest.get(url + "?" + formUrlEncodedString.toString());
        httpRequest.header("signature", signature);
        HttpResponse execute = httpRequest.execute();
        return execute.body();
    }

    private void deleteFile(File file, InputStream inputStream, FFmpegFrameGrabber grabber) {
        if (file != null) {
            file.delete();
        }

        if (inputStream != null) {
            try {
                inputStream.close();
            } catch (IOException e) {
            }
        }

        if (grabber != null) {
            try {
                grabber.stop();
            } catch (FrameGrabber.Exception e) {
            }
        }
    }


    private String getXunFeiSign(String formUrlEncodedString) throws Exception {

        byte[] signBytes = hmacSHA1Signature(xunFeiConfig.getAccessSecret(), formUrlEncodedString);

        return Base64.getEncoder().encodeToString(signBytes);

    }


    public static byte[] hmacSHA1Signature(String secret, String baseString)
            throws Exception {
        if (StrUtil.isEmpty(secret)) {
            throw new IOException("secret can not be empty");
        }
        if (StrUtil.isEmpty(baseString)) {
            return null;
        }
        Mac mac = Mac.getInstance("HmacSHA1");
        SecretKeySpec keySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8),
                "UTF-8");
        mac.init(keySpec);
        return mac.doFinal(baseString.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public String translate(String text, String from, String to) {
        if (StrUtil.isEmpty(text)) {
            log.warn("翻译文本为空");
            return "";
        }

        try {
            // 构建请求体
            JSONObject requestBody = buildTranslateRequestBody(text, from, to);

            // 发送请求并解析响应
            String response = sendTranslateRequest(requestBody);
            XunFeiTranslateResponseDto responseDto = parseTranslateResponse(response);

            if (responseDto.isSuccess()) {
                return responseDto.getResult();
            } else {
                log.error("讯飞翻译失败: code={}, message={}", responseDto.getCode(), responseDto.getMessage());
                return "";
            }
        } catch (Exception e) {
            log.error("讯飞翻译异常: text={}, from={}, to={}", text, from, to, e);
            return "";
        }
    }

    /**
     * 构建翻译请求体（按照讯飞API文档格式）
     */
    private JSONObject buildTranslateRequestBody(String text, String from, String to) {
        JSONObject requestBody = new JSONObject();

        // common部分
        JSONObject common = new JSONObject();
        common.put("app_id", xunFeiConfig.getAppId());
        requestBody.put("common", common);

        // business部分
        JSONObject business = new JSONObject();
        business.put("from", from);
        business.put("to", to);
        requestBody.put("business", business);

        // data部分 - 文本需要base64编码
        JSONObject data = new JSONObject();
        String encodedText = Base64.getEncoder().encodeToString(text.getBytes(StandardCharsets.UTF_8));
        data.put("text", encodedText);
        requestBody.put("data", data);

        return requestBody;
    }


    /**
     * 发送翻译请求（按照官方demo的鉴权方式）
     */
    private String sendTranslateRequest(JSONObject requestBody) throws Exception {
        String requestBodyStr = requestBody.toJSONString();

        // 按照官方demo的时间格式 - 使用GMT时区
        SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = format.format(new Date());

        // 计算请求体的SHA256摘要 - 按照官方demo格式
        String digest = "SHA-256=" + signBody(requestBodyStr);

        // 构建签名字符串 - 完全按照官方demo格式
        String host = "itrans.xfyun.cn";
        StringBuilder builder = new StringBuilder("host: ").append(host).append("\n")
                .append("date: ").append(date).append("\n")
                .append("POST ").append("/v2/its").append(" HTTP/1.1").append("\n")
                .append("digest: ").append(digest);
//        StringBuilder builder = new StringBuilder("host date request-line digest");

        // 使用HMAC-SHA256生成签名 - 按照官方demo
        String signature = hmacSign(builder.toString(), xunFeiConfig.getAccessSecret());

        // 构建Authorization头 - 完全按照官方demo格式
        String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"",
                xunFeiConfig.getAccessKey(), "hmac-sha256", "host date request-line digest", signature);

        log.info("翻译请求Authorization: {}", authorization);
        log.info("翻译请求Date: {}", date);
        log.info("翻译请求Digest: {}", digest);

        // 发送HTTP请求
        HttpRequest request = HttpRequest.post(TRANSLATE_URL);
        request.header("Authorization", authorization);
        request.header("Content-Type", "application/json");
        request.header("Accept", "application/json,version=1.0");
        request.header("Host", host);
        request.header("Date", date);
        request.header("Digest", digest);
        request.body(requestBodyStr);

        log.info("翻译请求URL: {}", TRANSLATE_URL);
        log.info("翻译请求体: {}", requestBodyStr);

        HttpResponse response = request.execute();

        if (response.getStatus() != 200) {
            log.error("翻译请求失败: status={}, body={}", response.getStatus(), response.body());
            throw new RuntimeException("翻译请求失败: " + response.getStatus());
        }

        String responseBody = response.body();
        log.info("翻译响应: {}", responseBody);
        return responseBody;
    }


    /**
     * 对body进行SHA-256加密（按照官方demo）
     */
    private String signBody(String body) throws Exception {
        MessageDigest messageDigest;
        String encodestr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(body.getBytes("UTF-8"));
            encodestr = Base64.getEncoder().encodeToString(messageDigest.digest());
        } catch (Exception e) {
            log.error("SHA-256加密失败", e);
            throw e;
        }
        return encodestr;
    }

    /**
     * hmacsha256加密（按照官方demo）
     */
    private String hmacSign(String signature, String apiSecret) throws Exception {
        Charset charset = Charset.forName("UTF-8");
        Mac mac = Mac.getInstance("hmacsha256");
        SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(signature.getBytes(charset));
        return Base64.getEncoder().encodeToString(hexDigits);
    }

    /**
     * 解析翻译响应（按照讯飞API文档格式）
     */
    private XunFeiTranslateResponseDto parseTranslateResponse(String response) {
        try {
            JSONObject jsonResponse = JSONObject.parseObject(response);

            XunFeiTranslateResponseDto responseDto = new XunFeiTranslateResponseDto();
            responseDto.setCode(jsonResponse.getInteger("code"));
            responseDto.setMessage(jsonResponse.getString("message"));
            responseDto.setSid(jsonResponse.getString("sid"));

            // 解析翻译结果 - 按照文档格式：data.result.trans_result.dst
            if (jsonResponse.containsKey("data") && jsonResponse.getJSONObject("data") != null) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data.containsKey("result") && data.getJSONObject("result") != null) {
                    JSONObject result = data.getJSONObject("result");
                    if (result.containsKey("trans_result") && result.getJSONObject("trans_result") != null) {
                        JSONObject transResult = result.getJSONObject("trans_result");
                        responseDto.setResult(transResult.getString("dst"));
                    }
                }
            }

            return responseDto;
        } catch (Exception e) {
            log.error("解析翻译响应失败: {}", response, e);
            XunFeiTranslateResponseDto errorResponse = new XunFeiTranslateResponseDto();
            errorResponse.setCode(-1);
            errorResponse.setMessage("解析响应失败");
            return errorResponse;
        }
    }
}
