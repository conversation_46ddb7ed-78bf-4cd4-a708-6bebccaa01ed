package com.xt.hsk.module.thirdparty.controller.app.sign;

import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.module.thirdparty.api.XunFeiApi;
import com.xt.hsk.module.thirdparty.dto.xunfei.XunFeiTranslateRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 翻译控制器
 */
@Tag(name = "翻译接口")
@RestController
@RequestMapping("/translate")
@Slf4j
public class TranslateController {

    @Autowired
    private XunFeiApi xunFeiApi;

    @Operation(summary = "通用翻译")
    @PostMapping("/general")
    public CommonResult<String> translate(@RequestBody XunFeiTranslateRequestDto request) {
        try {
            String result = xunFeiApi.translate(request.getText(), request.getFrom(), request.getTo());
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("翻译失败", e);
            return CommonResult.error("翻译失败: " + e.getMessage());
        }
    }
//
//    @Operation(summary = "中文翻译为英文")
//    @PostMapping("/cn-to-en")
//    public CommonResult<String> translateChineseToEnglish(@RequestParam String text) {
//        try {
//            String result = translateUtils.translateChineseToEnglish(text);
//            return CommonResult.success(result);
//        } catch (Exception e) {
//            log.error("中文翻译为英文失败", e);
//            return CommonResult.error("翻译失败: " + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "中文翻译为越南语")
//    @PostMapping("/cn-to-vi")
//    public CommonResult<String> translateChineseToVietnamese(@RequestParam String text) {
//        try {
//            String result = translateUtils.translateChineseToVietnamese(text);
//            return CommonResult.success(result);
//        } catch (Exception e) {
//            log.error("中文翻译为越南语失败", e);
//            return CommonResult.error("翻译失败: " + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "英文翻译为中文")
//    @PostMapping("/en-to-cn")
//    public CommonResult<String> translateEnglishToChinese(@RequestParam String text) {
//        try {
//            String result = translateUtils.translateEnglishToChinese(text);
//            return CommonResult.success(result);
//        } catch (Exception e) {
//            log.error("英文翻译为中文失败", e);
//            return CommonResult.error("翻译失败: " + e.getMessage());
//        }
//    }
//
//    @Operation(summary = "越南语翻译为中文")
//    @PostMapping("/vi-to-cn")
//    public CommonResult<String> translateVietnameseToChinese(@RequestParam String text) {
//        try {
//            String result = translateUtils.translateVietnameseToChinese(text);
//            return CommonResult.success(result);
//        } catch (Exception e) {
//            log.error("越南语翻译为中文失败", e);
//            return CommonResult.error("翻译失败: " + e.getMessage());
//        }
//    }

//    @Operation(summary = "批量翻译")
//    @PostMapping("/batch")
//    public CommonResult<String[]> batchTranslate(@RequestParam String[] texts,
//                                                 @RequestParam String from,
//                                                 @RequestParam String to) {
//        try {
//            String[] results = translateUtils.batchTranslate(texts, from, to);
//            return CommonResult.success(results);
//        } catch (Exception e) {
//            log.error("批量翻译失败", e);
//            return CommonResult.error("翻译失败: " + e.getMessage());
//        }
//    }
}
