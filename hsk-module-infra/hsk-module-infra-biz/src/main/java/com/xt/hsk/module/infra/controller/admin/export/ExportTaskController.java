package com.xt.hsk.module.infra.controller.admin.export;

import static com.xt.hsk.framework.common.pojo.CommonResult.success;

import com.fhs.core.trans.anno.TransMethodResult;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xt.hsk.framework.common.constants.LogRecordType;
import com.xt.hsk.framework.common.pojo.CommonResult;
import com.xt.hsk.framework.common.pojo.PageResult;
import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskPageReqVO;
import com.xt.hsk.module.infra.controller.admin.export.vo.ExportTaskRespVO;
import com.xt.hsk.module.infra.convert.export.ExportTaskConvert;
import com.xt.hsk.module.infra.dal.dataobject.export.ExportTaskDO;
import com.xt.hsk.module.infra.service.export.ExportTasksService;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 导出任务管理
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@RestController
@RequestMapping("/system/export-task")
public class ExportTaskController {

    @Resource
    private ExportTasksService exportTasksService;

    /**
     * 分页查询导出任务列表
     */
    @PostMapping("/page")
    @TransMethodResult
    @PreAuthorize("@ss.hasPermission('system:export-task:query')")
    public CommonResult<PageResult<ExportTaskRespVO>> getExportTaskPage(
        @Valid @RequestBody ExportTaskPageReqVO pageReqVO) {
        PageResult<ExportTaskDO> pageResult = exportTasksService.getExportTaskPage(pageReqVO);
        List<ExportTaskDO> resultList = pageResult.getList();
        List<ExportTaskRespVO> exportTaskRespVOList = ExportTaskConvert.INSTANCE.convertToRespVOPage(
            resultList);
        return success(new PageResult<>(exportTaskRespVOList, pageResult.getTotal()));
    }

    /**
     * 删除导出任务
     */
    @LogRecord(type = LogRecordType.EXPORT_TASK_TYPE, bizNo = "{{#id}}", success = "删除导出任务")
    @DeleteMapping("/delete")
    @PreAuthorize("@ss.hasPermission('system:export-task:delete')")
    public CommonResult<Boolean> deleteExportTask(@RequestParam("id") Long id) {
        return success(exportTasksService.removeById(id));
    }

} 