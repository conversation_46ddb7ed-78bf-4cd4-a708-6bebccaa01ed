package com.xt.hsk.module.infra.controller.admin.export.vo;

import com.xt.hsk.framework.common.pojo.PageParam;
import com.xt.hsk.module.infra.enums.export.ExportTaskTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 导出任务分页查询 Request VO
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Schema(description = "管理后台 - 导出任务分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ExportTaskPageReqVO extends PageParam {

    /**
     * 导出类型 1.互动课导出任务 2.互动课单元导出任务 3.讲师导出任务 4.字词库导出任务 5.精品课导出任务 6.用户导出任务 7.精品课学员导出任务 8.精品课学习统计导出任务
     * 9.精品课课时学习统计导出任务 10.讲师精品课导出任务
     *
     * @see ExportTaskTypeEnum
     */
    private Integer type;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 操作人
     */
    private String creator;

    /**
     * 任务状态
     */
    private Integer status;
    /**
     * 创建人信息
     */
    private List<Long> creatorIds;
}