package com.xt.hsk.module.infra.controller.admin.export.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 导出任务 Response VO
 * 用于导出任务的响应对象
 *
 * <AUTHOR>
 * @since 2025/06/04
 */
@Data
public class ExportTaskRespVO implements VO {

    /**
     * 任务ID
     */
    private Long id;

    /**
     * 任务名称 例如：用户导出
     */
    private String taskName;

    /**
     * 导出类型 1-互动课 2-互动课单元 3-讲师
     */
    private Integer type;

    /**
     * 任务状态 0-未开始 1-执行中 2-完成 3-失败 4-已过期 5-已取消
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     * 记录创建该导出任务的用户账号
     */
    @Trans(type = TransType.SIMPLE, targetClassName = "com.xt.hsk.module.system.dal.dataobject.user.AdminUserDO", fields = "nickname", ref = "creatorName")
    private String creator;

    /**
     * 创建人名称
     * 记录创建该导出任务的用户名称
     */
    private String creatorName;

    /**
     * 下载链接
     * 导出文件的下载地址
     */
    private String downloadUrl;

    /**
     * 文件大小(字节)
     * 导出文件的大小，单位为字节
     */
    private Long fileSize;

    /**
     * 错误信息
     * 记录任务执行失败时的错误信息
     */
    private String error;

    /**
     * 任务开始时间
     * 记录导出任务开始执行的时间
     */
    private LocalDateTime startTime;

    /**
     * 任务完成时间
     * 记录导出任务执行完成的时间
     */
    private LocalDateTime finishedAt;
} 