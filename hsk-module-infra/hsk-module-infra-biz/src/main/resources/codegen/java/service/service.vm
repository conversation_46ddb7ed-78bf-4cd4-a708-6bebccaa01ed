package ${basePackage}.module.${table.moduleName}.service.${table.businessName};

import com.baomidou.mybatisplus.extension.service.IService;
import ${basePackage}.module.${table.moduleName}.dal.dataobject.${table.businessName}.${table.className}DO;
import com.xt.hsk.framework.common.pojo.PageResult;
import jakarta.validation.Valid;
import com.xt.hsk.module.edu.controller.admin.${table.businessName}.vo.${table.className}PageReqVO;
## 特殊：主子表专属逻辑
#foreach ($subTable in $subTables)
import ${basePackage}.module.${subTable.moduleName}.dal.dataobject.${subTable.businessName}.${subTable.className}DO;
#end

/**
 * ${table.classComment} Service 接口
 *
 * <AUTHOR>
 */
public interface ${table.className}Service extends IService<${table.className}DO> {
   PageResult<${table.className}DO> selectPage(@Valid ${table.className}PageReqVO pageReqVO);

}